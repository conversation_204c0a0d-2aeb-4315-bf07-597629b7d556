# 字节阵数据接口使用说明

## 目录
- [字节阵数据接口使用说明](#字节阵数据接口使用说明)
  - [目录](#目录)
  - [接口概述](#接口概述)
  - [接口认证](#接口认证)
  - [获取并保存数据接口](#获取并保存数据接口)
    - [基本信息](#基本信息)
    - [请求参数](#请求参数)
    - [请求示例](#请求示例)
    - [响应格式](#响应格式)
    - [Python调用示例](#python调用示例)
  - [获取数据接口](#获取数据接口)
    - [基本信息](#基本信息-1)
    - [请求参数](#请求参数-1)
    - [请求示例](#请求示例-1)
    - [响应格式](#响应格式-1)
    - [Python调用示例](#python调用示例-1)
  - [数据管理接口](#数据管理接口)
    - [基本信息](#基本信息-2)
    - [请求参数](#请求参数-2)
    - [请求示例](#请求示例-2)
    - [响应格式](#响应格式-2)
    - [Python调用示例](#python调用示例-2)
  - [SQL测试接口](#sql测试接口)
    - [基本信息](#基本信息-3)
    - [请求参数](#请求参数-3)
    - [请求示例](#请求示例-3)
    - [响应格式](#响应格式-3)
    - [Python调用示例](#python调用示例-3)
  - [错误码说明](#错误码说明)
  - [注意事项](#注意事项)

## 接口概述
该接口提供了字节阵数据的查询和管理功能，包括：
- 从ERP获取数据并保存到数据库（fetch_and_save）
- 从ERP获取数据但不保存（fetch_data）
- 执行数据增删改操作（manage）
- SQL测试查询（test_sql）

## 接口认证
所有接口都需要在请求头中携带API密钥进行认证：
- 请求头参数：`X-API-Key`
- 参数说明：API访问密钥
- 是否必填：是

## 获取并保存数据接口

### 基本信息
- 接口URL：`/zijiezhen/fetch_and_save`
- 请求方式：POST
- 接口描述：从ERP接口获取数据并保存到数据库

### 请求参数
```json
{
    "table_name": "目标表名",
    "primary_keys": ["主键字段1", "主键字段2"],
    "beforeSql": "前置SQL语句"
    "sql": "SELECT语句"
}
```

### 请求示例
```json
{
    "table_name": "tb___f_90b51c2dd228013b",
    "primary_keys": ["f_sn"],
    "beforeSql": "",
    "sql": "SELECT CONVERT(VARCHAR(19), GETDATE(), 21) AS 记录时间, f_sn, f_date, f_departmentid FROM tb___f_90b51c2dd228013b WHERE f_status > 0 AND f_190e7dd6ca17e7af IN ('拼多多', '淘宝天猫', 'Temu')"
}
```

### 响应格式
```json
{
    "code": 200,
    "message": "操作结果描述",
    "data": {
        "total_fetched": 100,
        "total_saved": 100,
        "table_name": "tb___f_90b51c2dd228013b"
    }
}
```

### Python调用示例
```python
import requests
import json

def fetch_and_save_data():
    url = "https://4689cn93cl12.vicp.fun/api/zijiezhen/fetch_and_save"
    
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": "5a2f4fda3ab24bd0b10067ac701fecfd"
    }
    
    data = {
        "table_name": "tb___f_90b51c2dd228013b",
        "primary_keys": ["f_sn"],
        "beforeSql": "",
        "sql": """
        SELECT 
            CONVERT(VARCHAR(19), GETDATE(), 21) AS 记录时间,
            f_sn, f_date, f_departmentid 
        FROM tb___f_90b51c2dd228013b 
        WHERE f_status > 0
            AND f_190e7dd6ca17e7af IN ('拼多多', '淘宝天猫', 'Temu')
        """
    }
    
    try:
        response = requests.post(
            url, 
            headers=headers,
            json=data
        )
        
        # 检查响应状态
        if response.status_code == 200:
            result = response.json()
            print(f"请求成功: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return result
        else:
            print(f"请求失败: {response.status_code}")
            print(response.text)
            return None
            
    except Exception as e:
        print(f"发生错误: {str(e)}")
        return None

# 调用示例
if __name__ == "__main__":
    fetch_and_save_data()
```

## 获取数据接口

### 基本信息
- 接口URL：`/zijiezhen/fetch_data`
- 请求方式：POST
- 接口描述：从ERP接口获取数据，但不进行数据库保存操作。

### 请求参数
```json
{
    "sql": "SQL查询语句"
}
```

### 请求示例
```json
{
    "sql": "SELECT CONVERT(VARCHAR(19), GETDATE(), 21) AS 记录时间, f_sn, f_date, f_departmentid FROM tb___f_90b51c2dd228013b WHERE f_status > 0 AND f_190e7dd6ca17e7af IN ('拼多多', '淘宝天猫', 'Temu')"
}
```

### 响应格式
```json
{
    "code": 200,
    "message": "数据获取成功, 共获取100条记录",
    "data": [
        {
            "记录时间": "2024-07-30 10:00:00",
            "f_sn": "SN12345",
            "f_date": "2024-07-30",
            "f_departmentid": "Dept01"
        }
    ]
}
```

### Python调用示例
```python
import requests
import json

def fetch_data(sql: str):
    """
    函数名称：获取数据

    概述: 调用API接口，通过SQL查询ERP数据
    详细描述: 构造POST请求，携带SQL语句，调用远程API，返回查询到的数据
    调用的函数: 无
    参数:
        sql (str): 查询SQL语句
    返回值:
        dict: 接口返回的结果字典
    异常:
        Exception: 网络请求或数据处理异常
    修改时间: 2025-07-15 12:25
    """
    url = "https://4689cn93cl12.vicp.fun/api/zijiezhen/fetch_data"
    
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": "5a2f4fda3ab24bd0b10067ac701fecfd"
    }
    
    data = {
        "sql": sql
    }
    
    try:
        response = requests.post(
            url=url,
            headers=headers,
            json=data
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"请求成功: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return result
        else:
            print(f"请求失败: {response.status_code}")
            print(response.text)
            return None
            
    except Exception as e:
        print(f"发生错误: {str(e)}")
        return None

# 调用示例
if __name__ == "__main__":
    fetch_sql = """
    SELECT 
        CONVERT(VARCHAR(19), GETDATE(), 21) AS 记录时间,
        f_sn, f_date, f_departmentid 
    FROM tb___f_90b51c2dd228013b 
    WHERE f_status > 0
        AND f_190e7dd6ca17e7af IN ('拼多多', '淘宝天猫', 'Temu')
    """
    fetch_data(fetch_sql)
```

## 数据管理接口

### 基本信息
- 接口URL：`/zijiezhen/manage`
- 请求方式：POST
- 接口描述：执行数据增删改操作

### 请求参数
```json
{
    "sql": "SQL操作语句"
}
```

### 请求示例
```json
{
    "sql": "UPDATE tb___f_90b51c2dd228013b SET f_status = 0 WHERE f_sn = '12345'"
}
```

### 响应格式
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "affected_rows": 1
    }
}
```

### Python调用示例
```python
import requests
import json

def manage_data(sql: str):
    url = "https://4689cn93cl12.vicp.fun/api/zijiezhen/manage"
    
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": "5a2f4fda3ab24bd0b10067ac701fecfd"
    }
    
    data = {
        "sql": sql
    }
    
    try:
        response = requests.post(
            url=url,
            headers=headers,
            json=data
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"请求成功: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return result
        else:
            print(f"请求失败: {response.status_code}")
            print(response.text)
            return None
            
    except Exception as e:
        print(f"发生错误: {str(e)}")
        return None

# 调用示例
if __name__ == "__main__":
    # 更新数据示例
    update_sql = """
    UPDATE tb___f_90b51c2dd228013b 
    SET f_status = 0 
    WHERE f_sn = '12345'
    """
    manage_data(update_sql)
```

## SQL测试接口

### 基本信息
- 接口URL：`/zijiezhen/test_sql`
- 请求方式：POST
- 接口描述：使用字节阵测试编辑器获取数据，支持自动策略查询突破100条数据限制

### 请求参数
```json
{
    "sql": "SQL查询语句"
}
```

### 请求示例
```json
{
    "sql": "SELECT TOP 5 * FROM 近60天订单表"
}
```

### 响应格式
```json
{
    "code": 200,
    "message": "SQL测试编辑器数据获取成功, 共获取5条记录",
    "data": [
        {
            "字段1": "值1",
            "字段2": "值2",
            "字段3": "值3"
        },
        {
            "字段1": "值4",
            "字段2": "值5",
            "字段3": "值6"
        }
    ]
}
```

### Python调用示例
```python
import requests
import json

def test_sql_query(sql: str):
    url = "https://4689cn93cl12.vicp.fun/api/zijiezhen/test_sql"

    headers = {
        "Content-Type": "application/json",
        "X-API-Key": "5a2f4fda3ab24bd0b10067ac701fecfd"
    }

    data = {
        "sql": sql
    }

    try:
        response = requests.post(
            url=url,
            headers=headers,
            json=data
        )

        if response.status_code == 200:
            result = response.json()
            print(f"请求成功: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return result
        else:
            print(f"请求失败: {response.status_code}")
            print(response.text)
            return None

    except Exception as e:
        print(f"发生错误: {str(e)}")
        return None

# 调用示例
if __name__ == "__main__":
    # SQL查询示例
    test_sql = "SELECT TOP 5 * FROM 近60天订单表"
    test_sql_query(test_sql)
    """
    test_sql_query(complex_sql)
```

## 错误码说明
| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 参数错误（如SQL语句为空、表名为空等） |
| 401 | API密钥无效 |
| 500 | 服务器内部错误 |

## 注意事项
1. 所有请求必须包含有效的API密钥
2. SQL查询语句需要符合数据库规范
3. 主键列表必须包含至少一个字段
4. 表名必须是有效的数据库表名
5. SQL测试接口支持自动策略查询，当数据量接近100条时会自动切换到分页查询