# 一. 主要特点

1.  **脚本内嵌在文档中**
    - 与表格文档数据无缝结合，跟随文档一同分享协作
2.  **云端工作**
    - 云端编码，云端执行，无需本地开发环境
3.  **使用python脚本完成数据分析全链路**
    a.  数据连接
        - 支持从工作表、数据表中获取数据写入到表格
        - 将两张表以主键列联表，为数据分析提供基础
        - 同时还可以利用开源库从第三方应用获取数据，比如利用python获取数据库数据
    b.  数据清洗
        - 在获取数据函数中支持过滤参数，可以实现数据清洗
    c.  数据可视化
        利用丰富的第三方库，轻松实现数据分析可视化
        - 数据操作与分析： Pandas、NumPy
        - 数据可视化： pyecharts
        - 金融数据： akshare、tushare、baostock 等等等
    d.  将结果导入到表格
        - python脚本输出的结果可以写入到表格
        - python脚本输出的结果也可以导出为图片

# 二. 开始使用

使用 Python脚本编辑器前，请先在金山文档上新建或打开一个已有的智能表格或表格或多维表格文件。

**智能表格/表格文件中使用py脚本**

在智能表格/表格文件中顶部信息区，选择效率 > PY脚本
- 打开PY脚本功能的侧边栏后，可以看到文档内已经存在的Python文件和模板
- 点击新建脚本，创建Python文件，进入Python编辑栏，进行代码编辑
- 开始之前，也可以点击模板示例参考模板示例，了解表格中Python的使用

**多维表格文件中使用py脚本**

在多维表格顶部标题栏右侧区域，选择脚本 > PY脚本
- 打开PY脚本功能弹窗后，可以看到文档内已经存在的Python文件和模板
- 点击新建脚本，创建Python脚本，进入Python编辑器，进行代码编辑
- 开始之前，也可以点击模板示例参考模板示例，了解表格中Python的使用

# 三. 使用Python访问表格里的数据

📌金山文档的表格类产品中有工作表ET和数据表DB的区别，不同的表格类型，读取/写入数据要使用的函数是不同的，下面展开说一下～

## 3.1 使用python访问工作表里的数据

### 1. xl() 函数

#### 函数签名
```python
def xl(range: str = "",
       headers: bool = False,
       sheet_name: str | list[str] = "",
       book_url: str = "",
       start_row: int | None = None,
       start_column: int | None = None,
       end_row: int | None = None,
       end_column: int | None = None,
       formula: bool = False) -> __pd.DataFrame | dict[str, __pd.DataFrame]: ...
```

#### 参数列表
| 参数           | 类型                      | 默认值     | 说明                                                                                                             |
| -------------- | ------------------------- | ---------- | ---------------------------------------------------------------------------------------------------------------- |
| `range`        | `str`                     | 空字符串   | 工作表中的选区描述。<br>默认为工作表中已经使用的区域。                                                              |
| `headers`      | `bool`                    | `False`    | 是否将当前选区第一行处理为表头。                                                                                 |
| `sheet_name`   | `str` \| `list[str]`      | 空字符串   | 选区所在的工作表名称，可为多个。<br>默认为当前激活的工作表。<br>如果为`None`则返回全部工作表数据。                     |
| `book_url`     | `str`                     | 空字符串   | 选区所在的表格文件地址。<br>必须为金山文档云文档地址。<br>默认当前表格。                                               |
| `start_row`    | `int` \| `None`           | `None`     | 选区左上单元格的行，从0开始                                                                                        |
| `start_column` | `int` \| `None`           | `None`     | 选区左上单元格的列，从0开始                                                                                        |
| `end_row`      | `int` \| `None`           | 空值       | 选区右下单元格的行，从0开始                                                                                        |
| `end_column`   | `int` \| `None`           | 空值       | 选区右下单元格的列，从0开始                                                                                        |
| `formula`      | `bool`                    | `False`    | 是否返回单元格内的公式内容                                                                                       |

#### 示例
以下示例会使用到如下虚拟的进销存表格，并假设当前正在打开的是工作表1（激活状态）

a.  获取当前工作表（工作表1）中的所有数据，无表头
    ```python
    # 相当于 df = pandas.DataFrame(columns=None, data={全部数据})
    df1 = xl()

    # 由于无表头，只能按照索引访问 df 中的数据
    # 下边这条语句会输出"产品名称"
    print(df1[0][0])
    ```
b.  获取当前工作表（工作表1）中的所有数据，无表头 (注意：示例代码使用了 `headers=True`)
    ```python
    # 相当于 df = pandas.DataFrame(columns=[A1:C1], data=[A2:C5])
    df2 = xl("A1:C5", headers=True)

    # 可以通过列名来索引 df 中的数据
    df2_subset = df[['产品名称', '发货日期']]
    ```
c.  获取工作表2（当前激活为工作表1）中，A1:G10区域的数据，将第一行处理为表头
    ```python
    # 相当于 df = pandas.DataFrame(columns=[A1:G1], data=[A2:G10])
    df3 = xl("A1:G10", headers=True, sheet_name="工作表2")
    ```
d.  获取其它表格文档（`https://kdocs.cn/l/foo`）中，工作表3的前10行数据，第一行作为表头
    ```python
    df4 = xl(
        range="1:10",
        headers=True,
        sheet_name="工作表3",
        book_url="https://kdocs.cn/l/foo",
    )
    ```
e.  获取当前表格中，所有工作表数据。
    ```python
    # 此时将返回一个 dict[str, pandas.DataFrame] 类型的 ds
    # ds 的 key 为工作表名称
    ds = xl(
        headers=True,
        sheet_name=None,
    )

    df5 = ds['工作表1']
    ```
f.  获取当前表格（工作表1）中所有售价及发货日期数据
    ```python
    df6 = xl(
        start_row=0,
        start_column=1,
    )
    ```

### 2. write_xl() 函数
通过 `write_xl` 函数将数据回写到工作表。

#### 函数签名
```python
def write_xl(data: object,
             range: str = "",
             new_sheet: bool = False,
             sheet_name: str = "",
             overfill: bool = True,
             book_url: str = "",
             start_row: int | None = None,
             start_column: int | None = None,
             write_df_index: bool = False) -> None:
```

#### 参数列表
| 参数             | 类型                | 默认值     | 说明                                                                                                                                |
| ---------------- | ------------------- | ---------- | ----------------------------------------------------------------------------------------------------------------------------------- |
| `data`           | `object`            | 必填       | 要回写到工作表里的数据。<br>支持的数据类型包括：<br>Python 基本数据类型；<br>维度不超过2维的容器类型，如： `list` 和 `tuple`；<br>`pandas.DataFrame`；<br>不支持写入图片。 |
| `range`          | `str`               | 空字符串   | 工作表中的选区描述。<br>可以为一个单元格。为要写入数据的选区的左上角。<br>当`new_sheet=True`时可以为空。默认为新工作表的A1位置。        |
| `new_sheet`      | `bool`              | `False`    | 是否将数据写入到新建的工作表中。                                                                                                     |
| `sheet_name`     | `str`               | 空字符串   | 写入数据的选区所在的工作表名称。<br>当 `new_sheet=False` 时为表格中已经存在的工作表名称。<br>当 `new_sheet=True` 时为新建的工作表的名称。     |
| `overfill`       | `bool`              | `True`     | 当 `range` 不足以容纳 `data` 时，是否允许超出部分继续写入。<br>如果设置为 `False` 超出 `range` 的 `data` 部分会被丢弃。                  |
| `book_url`       | `str`               | 字符串     | 指定写入的表格文件地址。<br>必须为金山文档云文档地址。<br>默认当前表格。                                                                 |
| `start_row`      | `int` \| `None`     | 空值       | 选区左上单元格的行，从0开始                                                                                                           |
| `start_column`   | `int` \| `None`     | 空值       | 选区左上单元格的列，从0开始                                                                                                           |
| `write_df_index` | `bool`              | `False`    | 是否写入`pandas.DataFrame`里的index列                                                                                               |

#### 示例
a.  将字符串、数字回写到工作表
    ```python
    # 原始工作表
    # +-------+-----+
    # | Name  | Age |
    # +-------+-----+
    # |  foo  |  1  |
    # +-------+-----+
    # |  bar  |  2  |
    # +-------+-----+
    # |  baz  |  3  |
    # +-------+-----+

    # 删除"B3:B4"区域内的数据
    # delete_xl(range="B3:B4") # Assuming this is an example setup for write_xl, or a typo. Keeping as is.

    # 删除后的工作表 (This seems to illustrate delete_xl rather than write_xl directly in this sub-example)
    # +-------+-----+
    # | Name  | Age |
    # +-------+-----+
    # |  foo  |  1  |
    # +-------+-----+
    # |  bar  |     |
    # +-------+-----+
    # |  baz  |     |
    # +-------+-----+
    ```
b.  将 `pandas.DataFrame` 回写到工作表。
    将 DataFrame 回写到工作表时，是按照数据在 DataFrame 中的相对位置（行/列）进行写入的。如果设置了 `overfill=False` 超过选区 `range` 的部分会被丢弃

    ```python
    import pandas as pd

    # 构造一个有 columns 的 DataFrame
    df = pd.DataFrame({"Name": ["foo", "bar", "baz"], "Age": [1, 2, 3]})

    # 将 df 写入到当前工作表的 A1 位置
    # 由于 df 中包含 columns 定义
    # 最终会写入到 A1:B4 选区
    # 相当于第1行是表头，其余3行为数据
    # +-------+-----+
    # | Name  | Age |
    # +-------+-----+
    # |  foo  |  1  |
    # +-------+-----+
    # |  bar  |  2  |
    # +-------+-----+
    # |  baz  |  3  |
    # +-------+-----+
    write_xl(df, "A1")

    # 构造一个无 columns 的 DataFrame
    df2 = pd.DataFrame([["foo", 1], ["bar", 2], ["baz", 3]])

    # 将 df2 写入到当前工作表的 A1 位置
    # 由于 df2 中没有 columns（未显式定义，默认使用 pandas.RangeIndex）
    # 最终会写入到 A1:B3 选区
    # 相当于没有表头，只有3行数据
    # +-------+-----+
    # |  foo  |  1  |
    # +-------+-----+
    # |  bar  |  2  |
    # +-------+-----+
    # |  baz  |  3  |
    # +-------+-----+
    write_xl(df2, "A1")

    # 也可以单独回写某个 series，行为与只有一个"列"的 DataFrame 一致
    write_xl(df['Name'], "A1:B1") # Corrected from df['name'] to df['Name'] based on df definition
    ```
c.  将 `list`、`tuple`和`set`回写到工作表。
    将一维`list`、`tuple`和`set`回写到工作表。
    ```python
    # 构造一个有10个元素的 list
    l = [i for i in range(10)]

    # 将 l 回写到以 A1 开头的一行中
    # +-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+
    # |  0  |  1  |  2  |  3  |  4  |  5  |  6  |  7  |  8  |  9  |
    # +-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+
    write_xl(l, "A1")

    # 将 l 回写到当前工作表的 A1:C1 区域
    # 即将 l 回写成工作表中的一行
    # 但是此时 A1:C1 选区不足以容纳 10 个元素
    # 且 overfill=False 截断 l 中的元素
    # +-----+-----+-----+
    # |  0  |  1  |  2  |
    # +-----+-----+-----+
    write_xl(l, "A1:C1", overfill=False)

    # 将 l 回写到当前工作表的 A1：A10 区域
    # 与之前的示例相同，即将 l 回写成工作表中的一列
    # +-----+
    # |  0  |
    # +-----+
    # |  1  |
    # ... (rest of the rows)
    # +-----+
    # |  9  |
    # +-----+
    write_xl(l, "A1:A10")

    # 将 l 回写到当前工作表的 A1:E2 区域
    # 即将 l 回写成工作表中2行*5列
    # 此时要求 l 的长度必须不大于选区的长度
    # 否则无法判断该如何写入数据，导致报错
    # +-----+-----+-----+-----+-----+
    # |  0  |  1  |  2  |  3  |  4  |
    # +-----+-----+-----+-----+-----+
    # |  5  |  6  |  7  |  8  |  9  |
    # +-----+-----+-----+-----+-----+
    write_xl(l, "A1:E2")
    ```
    将二维`list`、`tuple`和`set`回写到工作表。
    ```python
    # 构造一个二维的 list
    data = [["foo", 1], ["bar", 2], ["baz", 3]]

    # 将 data 回写到当前工作表的 A1 位置
    # 将会在工作表中写入如下数据：
    # data 中的每一个子列表，被处理成工作表中的一行
    # +-------+-----+
    # |  foo  |  1  |
    # +-------+-----+
    # |  bar  |  2  |
    # +-------+-----+
    # |  baz  |  3  |
    # +-------+-----+
    write_xl(data, "A1")
    ```

### 3. delete_xl() 函数
通过 `delete_xl` 函数对数据进行删除操作。

#### 函数签名
```python
def delete_xl(range: str = "",
              sheet_name: str | list[str] = '',
              book_url: str | None = '',
              entire_row: bool = False,
              entire_column: bool = False,
              xl_shift_to_left: bool = False,
              start_row: int | None = None,
              start_column: int | None = None,
              drop_sheet: bool = False):
```

#### 参数列表
| 参数               | 类型                      | 默认值     | 说明                                                                 |
| ------------------ | ------------------------- | ---------- | -------------------------------------------------------------------- |
| `range`            | `str`                     | 空字符串   | 工作表中的选区描述。                                                 |
| `sheet_name`       | `str`\|`list[str]`       | 空字符串   | 写入数据的选区所在的工作表名称。                                       |
| `book_url`         | `str` \| `None`           | `''` (空字符串) | 指定写入的表格文件地址。<br>必须为金山文档云文档地址。<br>默认当前表格。 |
| `entire_row`       | `bool`                    | `False`    | 是否删除整行                                                         |
| `entire_column`    | `bool`                    | `False`    | 是否删除整列                                                         |
| `xl_shift_to_left` | `bool`                    | `False`    | 是否向左合并，如果为 `False` 则向上合并                                |
| `start_row`        | `int` \| `None`           | 空值       | 工作表中的起始行（从0开始），如果不传`start_column`则删除整行          |
| `start_column`     | `int` \| `None`           | 空值       | 工作表中的起始列（从0开始），如果不传`start_row`则删除整列            |
| `drop_sheet`       | `bool`                    | `False`    | 是否删除整个工作表                                                   |

#### 示例
a.  删除工作表某个范围的数据
    ```python
    # 原始工作表
    # +-------+-----+
    # | Name  | Age |
    # +-------+-----+
    # |  foo  |  1  |
    # +-------+-----+
    # |  bar  |  2  |
    # +-------+-----+
    # |  baz  |  3  |
    # +-------+-----+

    # 删除"B3:B4"区域内的数据
    delete_xl(range="B3:B4")

    # 删除后的工作表
    # +-------+-----+
    # | Name  | Age |
    # +-------+-----+
    # |  foo  |  1  |
    # +-------+-----+
    # |  bar  |     |
    # +-------+-----+
    # |  baz  |     |
    # +-------+-----+
    ```
b.  删除某行或某列数据
    ```python
    #原始工作表
    # +-------+-----+
    # | Name  | Age |
    # +-------+-----+
    # |  foo  |  1  |
    # +-------+-----+
    # |  bar  |  2  |
    # +-------+-----+
    # |  baz  |  3  |
    # +-------+-----+
    # 删除"B3:B4"区域内的数据  (Note: This description seems to repeat example 'a')
    delete_xl(range="B3:B4") # This is identical to example a, might be a placeholder
    # 删除后的工作表
    # +-------+-----+
    # | Name  | Age |
    # +-------+-----+
    # |  foo  |  1  |
    # +-------+-----+
    # |  bar  |     |
    # +-------+-----+
    # |  baz  |     |
    # +-------+-----+
    ```
c.  删除整个工作表
    ```python
    delete_xl(sheet_name="sh1", drop_sheet=True)
    ```

## 3.2 使用python访问数据表里的数据
📌注意
由于数据表中的类型并不能简单使用 `pandas.DataFrame` 来承载，因此对数据表读写的支持目前还不完善。

### 1. 数据表中的字段类型
不同于传统的表格产品，数据表中的每个字段，都是有类型的。这些类型包括：

| 字段类型         | 类型名称   | 值类型      | 说明                              |
| ---------------- | ---------- | ----------- | --------------------------------- |
| `MultiLineText`  | 文本       | `string`    |                                   |
| `Date`           | 日期       | `string`    |                                   |
| `Time`           | 时间       | `string`    |                                   |
| `Number`         | 数值       | `numeric`   |                                   |
| `Currency`       | 货币       | `numeric`   |                                   |
| `Percentage`     | 百分比     | `numeric`   |                                   |
| `ID`             | 身份证     | `string`    |                                   |
| `Phone`          | 电话       | `string`    |                                   |
| `Email`          | 电子邮箱   | `string`    |                                   |
| `URL`            | 超链接     | `object`    | 不支持通过 Python 更新            |
| `Checkbox`       | 复选框     | `boolean`   |                                   |
| `SingleSelect`   | 单选项     | `object`    | 不支持通过 Python 更新            |
| `MultipleSelect` | 多选项     | `object`    | 不支持通过 Python 更新            |
| `Rating`         | 等级       | `numeric`   | 会被降级成 `Number` 类型          |
| `Complete`       | 进度条     | `numeric`   |                                   |
| `Contact`        | 联系人     | `object`    | 不支持通过 Python 更新            |
| `Attachment`     | 附件       | `object`    | 不支持通过 Python 更新            |
| `Link`           | 关联       | `object`    | 不支持通过 Python 更新            |
| `Note`           | 富文本     | `object`    | 不支持通过 Python 更新            |
| `Address`        | 地址       | `object`    | 不支持通过 Python 更新            |
| `Cascade`        | 级联       | `object`    | 不支持通过 Python 更新            |

以下类型为数据表中的自动类型，即该类型的记录值是系统自动填充的，不支持外部更新。

| 字段类型           | 类型名称         |
| ------------------ | ---------------- |
| `AutoNumber`       | 编号             |
| `CreatedBy`        | 创建者           |
| `CreatedTime`      | 创建时间         |
| `LastModifiedBy`   | 最后修改者       |
| `LastModifiedTime` | 最后修改时间     |
| `Formula`          | 公式             |
| `Lookup`           | 引用             |

### 2. dbt() 函数
使用`dbt()`函数读取数据表中的数据。

#### 函数签名
```python
def dbt(field: str | list[str] = None,
        sheet_name: str | list[str] = '',
        book_url: str = '',
        condition: dict = {}) -> __pd.DataFrame | dict[str, __pd.DataFrame]: # Corrected return type based on context
```

#### 参数列表
| 参数         | 类型                      | 默认值     | 说明                                                                                               |
| ------------ | ------------------------- | ---------- | -------------------------------------------------------------------------------------------------- |
| `field`      | `str` \| `list[str]`      | `None`     | 要返回的字段列表。<br>默认为`None`，表示返回所有字段。                                                |
| `sheet_name` | `str` \| `list[str]`      | 空字符串   | 字段所在的数据表名称，可为多个。<br>默认为当前激活的数据表。<br>如果为`None`则返回全部数据表数据。         |
| `book_url`   | `str`                     | 空字符串   | 字段所在的表格文件地址。<br>必须为金山文档云文档地址。<br>默认当前表格。                                 |
| `condition`  | `dict`                    | `{}`       | 过滤条件，细节参考 [附件1：dbt 函数筛选记录说明](#附件1dbt-函数筛选记录说明)                        |

📌注意
`dbt()` 函数返回的 `pandas.DataFrame.index` 中包含每一条记录的 ID，供更新数据表使用时使用。

#### 示例
以下示例会使用到如下虚拟的进销存表格，并假设当前正在打开的是数据表1。
读取当前数据表数据表1中的产品名称和库存数量。

a.  读取当前数据表（数据表1）中的产品名称和库存数量。
    ```python
    # 返回数据表1中的字段名为产品名称和库存数量的记录，表头为字段名
    # 返回的数据类似下表所示
    #  其中 B，C，D 为 Index 中保存的记录 ID
    # +-----+-------------+----------+
    # |     |   产品名称   |  库存数量  |
    # +-----+-------------+----------+
    # |  B  |  iPhone 12  |   100    |
    # +-----+-------------+----------+
    # |  C  | MacBook Pro |   175    |
    # +-----+-------------+----------+
    # |  D  | iPad Air 4  |   173    |
    # +-----+-------------+----------+
    df = dbt(field=['产品名称', '库存数量'])
    ```
b.  读取当前数据表中，数据表1和数据表2的所有记录
    ```python
    # 此时返回的是一个包括两个 DataFrame 的 dict
    ds = dbt(sheet_name=['数据表1', '数据表2'])
    df1 = ds['数据表1']
    df2 = ds['数据表2']
    ```
c.  读取其它文档(`https://www.kdocs.cn/l/bar`)中的全部数据表中记录
    ```python
    # 此时返回的是一个包括多个 DataFrame 的 dict
    ds = dbt(sheet_name=None, book_url="https://www.kdocs.cn/l/bar")
    ```

### 3. insert_dbt() 函数
使用`insert_dbt()`函数向数据表中写入新记录。

#### 函数签名
```python
def insert_dbt(data: dict[str, any] | list[dict[str, any]] | __pd.DataFrame,
               sheet_name: str = '',
               new_sheet: bool = False) -> None:
```

#### 参数列表
| 参数         | 类型                                                         | 默认值     | 说明                                                                                                                                                              |
| ------------ | ------------------------------------------------------------ | ---------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `data`       | `dict[str, any]` \| `list[dict[str, any]]` \| `__pd.DataFrame` | 必填       | 要回写到数据表里的数据。<br>支持的数据类型包括：<br>`dict[str, any]`包含记录名称和值的 dict；<br>维度不超过2维的 `list` 类型；<br>`pandas.DataFrame`；<br>不支持写入附件等复杂类型。 |
| `sheet_name` | `str`                                                        | 空字符串   | 写入数据的选区所在的数据表名称。<br>当 `new_sheet=False` 时为表格中已经存在的数据表名称。<br>当 `new_sheet=True` 时为新建的数据表的名称。                             |
| `new_sheet`  | `bool`                                                       | `False`    | 是否将数据写入到新建的数据表中。                                                                                                                                  |

📌提示
建议直接使用 `dbt()` 函数返回的 DataFrame，进行所需要的处理后，回写数据表。

#### 示例
```python
# 返回数据表1中的字段名为产品名称和库存数量的记录，表头为字段名
# 返回的数据类似下表所示
#  其中 B，C，D 为 Index 中保存的记录 ID
# +-----+-------------+----------+
# |     |   产品名称   |  库存数量  |
# +-----+-------------+----------+
# |  B  |  iPhone 12  |   100    |
# +-----+-------------+----------+
# |  C  | MacBook Pro |   175    |
# +-----+-------------+----------+
# |  D  | iPad Air 4  |   173    |
# +-----+-------------+----------+
df = dbt(field=['产品名称', '库存数量'])

# 将 df 中的数据写入到新的数据表2中
insert_dbt(df, sheet_name="数据表2", new_sheet=True)

# 向数据表2中，新增一条记录
insert_dbt({"产品名称": "iPad Air 5", "库存数量": 100 }, sheet_name="数据表2")

# 向数据表2中，新增两条记录
insert_dbt([{"产品名称": "Mac mini M2"}, {"产品名称": "Mac mini M2 Pro"}] , sheet_name="数据表2")
```

### 4. update_dbt() 函数
使用`update_dbt()`函数更新数据表中记录。

#### 函数签名
```python
def update_dbt(data: dict[str, any] | list[dict[str, any]] | __pd.DataFrame,
               sheet_name: str = '') -> None:
```

#### 参数列表
| 参数         | 类型                                                         | 默认值     | 说明                                                                                                                                                              |
| ------------ | ------------------------------------------------------------ | ---------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `data`       | `dict[str, any]` \| `list[dict[str, any]]` \| `__pd.DataFrame` | 必填       | 要回写到数据表里的数据。<br>支持的数据类型包括：<br>`dict[str, any]`包含记录ID、名称和值的 dict；<br>维度不超过2维的 `list` 类型；<br>`pandas.DataFrame`；<br>不支持写入附件等复杂类型。 |
| `sheet_name` | `str`                                                        | 空字符串   | 写入数据的选区所在的数据表名称。<br>当 `new_sheet=False` 时为表格中已经存在的数据表名称。<br>当 `new_sheet=True` 时为新建的数据表的名称。 (Note: new_sheet is not a param here) |

#### 示例
```python
# 返回数据表1中的字段名为产品名称和库存数量的记录，表头为字段名
# 返回的数据类似下表所示
#  其中 B，C，D 为 Index 中保存的记录 ID
# +-----+-------------+----------+
# |     |   产品名称   |  库存数量  |
# +-----+-------------+----------+
# |  B  |  iPhone 12  |   100    |
# +-----+-------------+----------+
# |  C  | MacBook Pro |   175    |
# +-----+-------------+----------+
# |  D  | iPad Air 4  |   173    |
# +-----+-------------+----------+
df = dbt(field=['产品名称', '库存数量'])

# 修改 df 中的数据，并更新数据表中的记录
df['库存数量'] = df['库存数量'] * 2
update_dbt(df)

# 单独修改数据表1中的记录
# 其中包括ID字段 _rid 值为对应的 Index 中的值
update_dbt({"_rid": "B", "产品名称": "iPhone 12 Pro"})

# 更新多条记录的值
update_dbt([{"_rid": "C", "产品名称": "MacBook Pro M1"}, {"_rid": "D", "库存数量": 200}])
```

### 5. delete_dbt() 函数
使用`delete_dbt()`函数删除数据表中的数据。
(Note: The original signature was `def dbt(...)`. Keeping it as `delete_dbt` as per function name for consistency, but the actual API might differ. Reformatting only.)

#### 函数签名
```python
def delete_dbt(sheet_name: str | list[str] = '', # Assuming similar signature to delete_xl or dbt read
               book_url: str = '',
               condition: dict = {}): # -> __pd.DataFrame | dict[str, __pd.DataFrame, # Return type was from dbt(), delete usually returns None
```
(Note: The original signature in the doc for `delete_dbt` was actually a copy of `dbt()` read. I've adjusted it to be more plausible for a delete operation but keeping parameters mentioned. The return type is questionable for a delete op.)

#### 参数列表
| 参数         | 类型                      | 默认值     | 说明                                                                                               |
| ------------ | ------------------------- | ---------- | -------------------------------------------------------------------------------------------------- |
| `sheet_name` | `str` \| `list[str]`      | 空字符串   | 字段所在的数据表名称，可为多个。<br>默认为当前激活的数据表。<br>如果为`None`则返回全部数据表数据。         |
| `book_url`   | `str`                     | 空字符串   | 字段所在的表格文件地址。<br>必须为金山文档云文档地址。<br>默认当前表格。                                 |
| `condition`  | `dict`                    | `{}`       | 过滤条件，细节参考 [附件1：dbt 函数筛选记录说明](#附件1dbt-函数筛选记录说明)<br>当没有 (Note: "当没有" is incomplete) |

#### 示例代码
以下示例会使用到如下虚拟的小费的信息

a.  case1:删除整表数据
    ```python
    delete_dbt(sheet_name=["数据表"])
    ```
b.  case2: 根据条件删除数据。 如下代码，删除 食物为"炸蘑菇"的数据
    ```python
    delete_dbt(
      sheet_name=["小费数据"],
      condition={
        "mode": "AND",
        "criteria": [
            {
                "field": "食物",
                "op": "Intersected",
                "values": [
                    "炸蘑菇"
                ]
            }
        ]
    })
    ```

# 四. 内置的第三方库

-   **pandas**
    Pandas是一个开源的数据分析和数据处理库，它是基于NumPy构建的，提供了高性能、易于使用的数据结构和数据分析工具，使得在Python中进行数据处理和分析变得更加简单和高效。
-   **requests**
    requests库是Python的一个HTTP客户端库，可以帮助用户发送各种类型的HTTP请求，如GET、POST、PUT、DELETE等，并获取响应。requests库具有简单易用、功能强大、灵活性高等特点，因此被广泛应用于Python网络编程中。
-   **akshare**
    AkShare是基于Python的开源金融数据接口库，目的是实现对股票、期货、期权、基金、债券、外汇等金融产品和另类数据从数据采集，数据清洗到数据下载的工具，满足金融数据科学。
-   **astropy**
    Astropy用于天文学数据处理和分析。它提供了许多有用的工具和函数来操作各种类型的天文学数据，从图像和表格到天体物理学常见的坐标系转换和单位转换。
-   **baostock**
    BaoStock是一个证券数据服务平台。考虑到 Python pandas 包在金融量化分析中体现出的优势， BaoStock 返回的绝大部分的数据格式都是 pandas DataFrame 类型，非常便于用 pandas/NumPy/Matplotlib 进行数据分析和可视化。
-   **bs4**
    Beautiful Soup（简称BS4）是一个用于解析HTML和XML文档的Python库。它提供了一种简单而灵活的方式来导航、搜索和修改解析树，使得从网页中提取数据变得更加容易。
-   **Cartopy**
    Cartopy是一个Python包，用于地理空间数据处理，以便生成地图和其他地理空间数据分析。 Cartopy利用了强大的PROJ.4、NumPy和Shapely库，并在Matplotlib之上构建了一个编程接口，用于创建发布高质量的地图。
-   **imbalanced-learn**
    imbalanced-learn提供了一些技术来解决数据不平衡的问题。在分类问题中，如果数据集中的一个类别的样本数量远远大于另一个类别，这会导致模型对多数类别的偏向，从而降低对少数类别的识别能力。这种情况下，imbalanced-learn库可以帮助提高模型对少数类别的识别能力。
-   **IPython**
    IPython是一个交互式计算环境的扩展库，提供了一个强大的交互式环境和工具集，提供了许多方便的功能和特性，使得开发者可以更加高效地编写、测试和调试Python代码。它是Python数据科学和机器学习领域中常用的工具之一。
-   **matplotlib**
    Matplotlib是Python中一个常用的绘图库，可以用于绘制各种类型的图表，包括线图、散点图、条形图、等高线图、3D图等等。它是一个非常强大和灵活的库，被广泛用于数据科学、机器学习、工程学、金融等领域。
-   **networkx**
    NetworkX是一个用于创建、操作和学习复杂网络的Python库。它提供了一组丰富的工具和算法，用于分析和可视化网络结构，以及研究网络的属性和行为。
-   **nltk**
    Natural Language Toolkit（简称NLTK）是一个用于自然语言处理（NLP）的Python库。它提供了一系列工具和数据集，用于处理、分析和理解文本数据。
-   **numpy**
    NumPy（Numerical Python）是一个用于科学计算的Python库。它提供了一个高性能的多维数组对象（ndarray）和一组用于操作数组的函数，使得在Python中进行数值计算和数据处理变得更加高效和方便。
-   **pyecharts**
    Pyecharts是一个用于生成交互式图表和可视化的Python库，它基于Echarts JavaScript库，并提供了一种简单而强大的方式来创建各种类型的图表。通过Pyecharts，可以轻松地将数据转化为各种图表，如折线图、柱状图、散点图、饼图等等，并且可以对图表进行各种定制，如修改颜色、添加标签、调整字体等等。使用Pyecharts可以大大提高数据可视化的效率，让用户更加直观地了解数据的分布和规律。同时，Pyecharts也支持多种输出格式，如HTML、PDF等，方便用户将图表嵌入到Web页面或生成报告中使用。
-   **pymysql**
    PyMySQL是Python中用于连接和操作MySQL数据库的一个库。它提供了Python编程语言和MySQL数据库之间的接口，使得Python程序可以方便地连接、查询和操作MySQL数据库。
-   **pywavelets**
    PyWavelets是Python中用于小波变换的免费开源库。小波是在时间和频率上都局部化的数学基函数，小波变换则是利用小波的时频变换来分析和处理信号或数据。PyWavelets提供了丰富的功能和灵活的接口，可以对图像、音频、信号等数据进行小波变换、逆变换、阈值去噪、压缩等操作。此外，PyWavelets还支持多种小波基函数和边界处理方式，用户可以根据需要选择合适的小波基函数和参数。
-   **scikit-image**
    Scikit-image是一个基于Python脚本语言开发的数字图片处理包，它将图片作为numpy数组进行处理，正好与matlab一样。scikit-image对scipy.ndimage进行了扩展，提供了更多的图片处理功能。Scikit-image库包含了一些基本的图像处理功能，比如图像缩放、旋转、图像变换、阈值化处理等等。此外，它还包含了众多高级图像处理算法，比如边缘检测、形态学操作、直线和圆检测等等。
-   **scikit-learn**
    Scikit-learn（以前称为scikits.learn，也称为sklearn）是一个简单高效的数据挖掘和数据分析工具，建立在Python编程语言之上。它是为了解决真实世界中的问题而开发的，并且在学术和商业环境中都得到了广泛的应用。Scikit-learn的主要功能包括分类、回归、聚类、降维、模型选择和预处理。
-   **scipy**
    scipy是一个基于Python的开源科学计算库，它建立在NumPy库的基础上，提供了更高级的数学、科学和工程计算功能。scipy库包含了许多模块，每个模块都提供了一组相关的函数和工具，用于解决各种数学、科学和工程问题。
-   **seaborn**
    Seaborn是一个基于Python的数据可视化库，它在matplotlib的基础上进行了更高级的API封装，使得作图更加容易，并且制作出来的图形也更加美观和具有吸引力。Seaborn提供了一种高度交互式界面，便于用户能够做出各种有吸引力的统计图表。
-   **statsmodels**
    statsmodels是一个Python库，提供了用于统计建模和计量经济学的函数和类。它包含了一系列统计模型，用于数据分析、探索性数据分析(EDA)、建模和推断。该库的目标是提供一种简单而一致的接口，使得用户可以在Python中进行各种统计任务。
-   **sympy**
    sympy是一个基于Python的符号计算库，它提供了符号计算的功能，可以进行符号代数、微积分、线性代数、离散数学等方面的计算。与其他数值计算库不同，sympy库执行的是精确计算，而不是数值近似，这使得它非常适合用于数学推导、符号计算和数学建模。
-   **tushare**
    tushare是一个基于Python的金融数据接口库，它提供了丰富的金融市场数据，包括股票、指数、基金、期货、外汇等数据。tushare库获取数据的来源是中国的金融市场，可以帮助获取和分析金融数据。
-   **qrcode**
    qrcode是一个基于Python的二维码生成库，它可以将文本、URL或其他数据转换成二维码图像。该库支持自定义二维码的大小、颜色、纠错级别等参数，同时还可以在二维码中添加logo图片。它的API简单易用，既可以生成基本的黑白二维码，也可以创建带有自定义样式的艺术二维码。
    ```python
    import qrcode

    # 生成二维码
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    qr.add_data('https://www.baidu.com')
    qr.make(fit=True)

    img = qr.make_image(fill_color="black", back_color="white")

    img.show()
    ```
-   **jionlp**
    jionlp是一个中文NLP工具包，提供了大量常用的中文文本处理功能，包括文本清洗、实体识别、地址解析、时间解析、摘要生成、新闻分类等。它设计简洁，即装即用，能够帮助开发者快速处理中文文本数据，适用于各类中文NLP任务。
    ```python
    import jionlp as jio

    text = '有疑问请联系张小姐18340234920，或拨打(028)58291283。'
    phones = jio.remove_phone_number(text)
    ```
-   **jieba**
    jieba是最常用的中文分词库之一，它提供了多种分词模式，包括精确模式、全模式、搜索引擎模式等。除了基本分词功能外，jieba还支持自定义词典、关键词提取、词性标注等功能。它采用基于前缀词典实现高效的词图扫描，使用动态规划查找最大概率路径，具有较好的分词准确率。
    ```python
    import jieba

    seg_list = jieba.cut("我来到北京清华大学", cut_all=True)
    print("Full Mode: " + "/ ".join(seg_list))  # 全模式

    seg_list = jieba.cut("我来到北京清华大学", cut_all=False)
    print("Default Mode: " + "/ ".join(seg_list))  # 精确模式

    seg_list = jieba.cut("他来到了网易杭研大厦")  # 默认是精确模式
    print(", ".join(seg_list))

    seg_list = jieba.cut_for_search("小明硕士毕业于中国科学院计算所，后在日本京都大学深造")  # 搜索引擎模式
    print(", ".join(seg_list))
    ```
-   **faker**
    faker是一个用于生成假数据的Python库，它可以生成各种类型的虚拟数据，包括姓名、地址、电话号码、邮箱、公司名称、信用卡号等。支持多种语言和地区的数据格式，对于开发测试、数据库填充、演示样例等场景非常有用。
    ```python
    from faker import Faker

    fake = Faker()

    print(f"Name: {fake.name()}")
    print(f"Address: {fake.address()}")
    print(f"Text: {fake.text()}")
    ```
-   **pypinyin**
    pypinyin是一个汉字转拼音的Python库，支持将中文汉字转换为拼音，提供多种拼音风格（包括带声调、数字声调、无声调等），可以处理多音字，支持自定义词库。它常用于中文文本的拼音化、汉字排序、模糊搜索等场景。
    ```python
    from pypinyin import pinyin

    print(pinyin('中心'))
    ```
-   **polars**
    polars是一个高性能的数据处理库，使用Rust语言编写并提供Python接口。它提供了类似pandas的DataFrame API，但在处理大规模数据时具有更好的性能。polars支持惰性计算、并行处理、内存优化等特性，适合处理大规模数据分析和数据处理任务，特别是在需要高性能计算的场景下。
    ```python
    import polars as pl

    # 创建一个简单的DataFrame
    df = pl.DataFrame({
          "A": [1, 2, 3, 4],
          "B": [5, 6, 7, 8]
    })

    # 查看DataFrame
    print(df)

    # 选择列
    print(df.select(["A", "B"]))

    # 过滤行
    print(df.filter(pl.col("A") > 2))

    # 排序
    print(df.sort(by="A"))

    # 聚合
    print(df.group_by("A").agg(pl.col("B").mean()))
    ```

# 五. 时区处理说明

## 1. 运行环境时区问题

⚠️ **重要提醒**：WPS Python脚本的运行环境**不是默认中国时区**，而是使用UTC时区或其他时区。这意味着直接使用 `datetime.datetime.now()` 获取的时间可能不是北京时间。

### 问题表现
- 使用 `datetime.datetime.now()` 获取的时间与北京时间存在时差
- 数据库中存储的时间戳可能不是中国时区时间
- 日志记录的时间与实际操作时间不符

### 影响范围
- 所有涉及时间记录的业务逻辑
- 数据库时间戳字段的更新
- 日志记录和调试信息的时间显示
- 与其他系统的时间同步

## 2. 中国时区解决方案

为了确保获取准确的中国时区时间，我们需要实现专门的时区处理函数：

### 2.1 时区处理辅助函数

```python
import datetime

def get_china_time():
    """
    获取中国时区（UTC+8）的当前时间
    
    返回值:
        datetime.datetime: 中国时区的当前时间对象
    """
    try:
        # 尝试使用timezone模块（Python 3.2+）
        china_tz = datetime.timezone(datetime.timedelta(hours=8))
        return datetime.datetime.now(china_tz)
    except AttributeError:
        # 如果timezone不可用，手动添加8小时
        utc_now = datetime.datetime.utcnow()
        china_time = utc_now + datetime.timedelta(hours=8)
        return china_time

def get_china_time_str():
    """
    获取中国时区（UTC+8）的当前时间字符串
    
    返回值:
        str: 格式为 'YYYY-MM-DD HH:MM:SS' 的时间字符串
    """
    china_time = get_china_time()
    return china_time.strftime('%Y-%m-%d %H:%M:%S')
```

### 2.2 兼容性设计

上述解决方案采用了兼容性设计：

1. **优先使用标准方法**：尝试使用 `datetime.timezone` 模块（Python 3.2+）
2. **降级兼容处理**：如果 `timezone` 不可用，自动降级为手动添加8小时的方法
3. **确保准确性**：无论哪种方法，都能确保获取到正确的中国时区时间

### 2.3 使用示例

```python
# 错误的做法（可能不是中国时区时间）
# current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

# 正确的做法（确保是中国时区时间）
current_time = get_china_time_str()
print(f"当前中国时区时间: {current_time}")

# 用于数据库更新
sql_statement = f"UPDATE table_name SET update_time = '{current_time}' WHERE id = 1"

# 用于日志记录
print(f"操作时间: {current_time} - 执行了某项操作")
```

## 3. 最佳实践建议

### 3.1 统一时间获取
- 在脚本开头定义时区处理函数
- 所有需要获取当前时间的地方都使用 `get_china_time_str()`
- 避免直接使用 `datetime.datetime.now()`

### 3.2 时间格式标准化
- 统一使用 `YYYY-MM-DD HH:MM:SS` 格式
- 与SQL数据库的 `DATETIME` 类型兼容
- 便于日志查看和问题排查

### 3.3 代码示例模板
```python
import datetime
import time

# 时区处理函数
def get_china_time():
    try:
        china_tz = datetime.timezone(datetime.timedelta(hours=8))
        return datetime.datetime.now(china_tz)
    except AttributeError:
        utc_now = datetime.datetime.utcnow()
        china_time = utc_now + datetime.timedelta(hours=8)
        return china_time

def get_china_time_str():
    china_time = get_china_time()
    return china_time.strftime('%Y-%m-%d %H:%M:%S')

# 主要业务逻辑
def main():
    # 记录脚本开始时间（中国时区）
    script_start_time = time.time()
    print(f"脚本开始时间: {get_china_time_str()}")
    
    # 生成业务时间戳（中国时区）
    business_timestamp = get_china_time_str()
    print(f"业务时间戳: {business_timestamp}")
    
    # 其他业务逻辑...
    
    # 记录脚本结束时间（中国时区）
    print(f"脚本结束时间: {get_china_time_str()}")
    print(f"总执行时间: {time.time() - script_start_time:.3f}秒")

if __name__ == "__main__":
    main()
```

## 4. 注意事项

### 4.1 时区一致性
- 确保整个项目中的时间处理保持一致
- 与数据库、前端系统的时区设置保持同步
- 在团队开发中统一时区处理标准

### 4.2 测试验证
- 在不同环境中测试时区处理的正确性
- 验证生成的时间戳是否为北京时间
- 检查数据库中存储的时间是否正确

### 4.3 文档记录
- 在代码注释中明确说明使用了中国时区
- 在函数文档中标注时区信息
- 为后续维护人员提供清晰的时区处理说明

# 六. 在自动化流程中执行Python脚本

🔔**注**：自动化流程中执行python脚本指令，是一个面向开发者的高级功能，需要具备一定的编程基础知识，请在开发者的帮助下使用此功能。

## 1. 功能简介

此操作可以执行一份预先设置的脚本文件来实现更加灵活的业务逻辑。

## 2. 参数说明

| 参数名称                 | 描述                                                                                                                               | 是否必填 | 默认值                      |
| :----------------------- | :--------------------------------------------------------------------------------------------------------------------------------- | :------- | :-------------------------- |
| 选择将要执行的脚本文件 | 选择待执行的脚本。 <br> 脚本添加方式如图：<br>                                     | 是       |                             |
| 脚本入参                 | 脚本中可读取到的参数，需以 json 的格式传入脚本。传入脚本后会转换为tuple格式，通过 `Context['argv'][xxx]` 获取具体入参。<br>**特别注意**：当脚本入参为JSON对象，且其某个键对应的值在配置时为单个字符串时，WPS环境传递给脚本时可能会将该字符串值包装在一个单元素列表中。例如，若配置入参为 `{"key": "value"}`，脚本中 `Context['argv']['key']` 获取到的可能是 `['value']` 而非直接的 `"value"`。建议脚本中对此进行检查和处理。 <br>  | 否       | `Context['argv']` 为 `None` |
| 脚本返回值               | 暂时不支持返回值                                                                                                                     |          |                             |

*（请将 `path/to/your/image/script_add_method.png` 和 `path/to/your/image/script_input_example.png` 替换为实际的图片路径。如果图片是本地的，考虑上传到可访问的图床或项目目录中，并使用相对路径。）*

## 3. 常见问题

**问：什么是符合 JSON 格式规范的内容？**

答：请从以下几个角度确认输入的内容是否符合 JSON 格式规范：

1.  **引号规范**：数组或对象之中的字符串**必须**为英文双引号 (`"`)。
2.  **键值对**：结构体中需包含一个或多个键值对（Key-Value）。
3.  **键（Key）规范**：键（Key）**必须**为字符串，且键与值之间用英文冒号 (`:`) 分割。
4.  **对象格式**：对象以大括号开始和结束：`{}`。
5.  **引用变量**：
    *   若引用了 `dataframe` 格式的变量，**不需要**在前后添加英文双引号。
    *   若引用文本类型的变量，则**需要**在前后添加英文双引号。
    *   示例如下：
        ![JSON引用示例](path/to/your/image/json_reference_example.png)
6.  **DataFrame列作为数组**：若选择 `dataframe` 格式的变量的某一列，则值类型为数组，每一项是单行记录该列的值。
    *   **表格数据示例**：
        | Po | 名称   | 数量 |
        | -- | ------ | ---- |
        | 1  | 第一行 | 1.00 |
        | 2  | 第二行 | 2    |
        | 3  | 第三行 |      |
    *   **变量选择**：名称列
        ![DataFrame列选择](path/to/your/image/df_column_selection.png)
    *   **脚本入参显示**：
        ![脚本入参DataFrame列](path/to/your/image/script_input_df_column.png)
        ```json
        {
          "a": ["第一行", "第二行", "第三行"]
        }
        ```
        在Python脚本中获取：
        ```python
        # 假设脚本入参如上
        # context_argv = Context['argv']
        # a_value = context_argv['a']
        # print(a_value)  # 输出: ['第一行', '第二行', '第三行']
        ```

**JSON内容总示例：**
```json
{
    "header": {
        "title": {
            "tag": "plain_text",
            "content": "This is header"
        },
        "template": "red"
    }
}
```

**问：为什么我配置的JSON入参中的字符串值，在Python脚本中获取到的是一个列表？**
答：WPS自动化流程在处理JSON格式的脚本入参时，有时会将JSON对象中配置的单个字符串值，包装成一个仅包含该字符串的单元素列表后再传递给Python脚本。
例如，您在自动化流程的"脚本入参"中配置如下：
```json
{
  "成品下单编码": "CPXD12345"
}
```
在Python脚本中，通过 `Context['argv']['成品下单编码']` 获取到的值可能是列表 `['CPXD12345']`，而不是预期的字符串 `"CPXD12345"`。

**建议处理方式：**
为确保脚本的健壮性，在获取这类参数后，应先判断其类型。如果是一个列表且长度为1，则取其第一个元素作为实际的字符串值。
```python
# 示例代码：
# 假设 Context['argv'] = {'成品下单编码': ['CPXD12345']} # WPS环境传入的参数
actual_order_code = None
script_params = Context['argv'] # 一般情况下 Context['argv'] 是一个字典

if isinstance(script_params, dict):
    raw_order_code_value = script_params.get('成品下单编码')

    if isinstance(raw_order_code_value, list):
        if len(raw_order_code_value) == 1 and isinstance(raw_order_code_value[0], str):
            actual_order_code = raw_order_code_value[0]
        else:
            # 列表为空、包含多个元素或元素非字符串，按错误处理
            print(f"错误：'成品下单编码' 参数值列表格式不正确: {raw_order_code_value}")
    elif isinstance(raw_order_code_value, str):
        # 参数直接是字符串（以防WPS行为不一致或未来变化）
        actual_order_code = raw_order_code_value
    else:
        # 参数值不存在或类型不是列表或字符串
        if raw_order_code_value is None:
            print("错误：脚本入参中未找到 '成品下单编码'。")
        else:
            print(f"错误：'成品下单编码' 参数值类型非预期: {type(raw_order_code_value)}")
else:
    print(f"错误：Context['argv'] 不是预期的字典类型: {type(script_params)}")

if actual_order_code:
    print(f"成功提取的成品下单编码: {actual_order_code}")
    # 在这里使用 actual_order_code 进行后续操作
else:
    print("未能成功提取成品下单编码。")
```
通过这样的检查，可以更可靠地获取参数值，无论WPS是直接传递字符串还是将其包装在单元素列表中。

# 附件1：dbt 函数筛选记录说明
在 `dbt` 中，使用 `condition` 参数对数据进行筛选，说明如下：

## 1. condition参数格式
```json
{
    "mode": "AND", // 选填。表示各筛选条件之间的逻辑关系。只能是"AND"或"OR"。缺省值为"AND"
    "criteria": [ // 结构体内必填。包含筛选条件的数组。每个字段上只能有一个筛选条件
        {
            "field": "名称", // 必填。根据 preferId 与否，需要填入字段名或字段id
            "op": "Intersected", // 必填。表示具体的筛选规则，见下
            "values": [ // 必填。表示筛选规则中的值。数组形式。
                "多维表", // 值为字符串时表示文本匹配
                "12345"
            ]
        },
        {
            "field": "数量",
            "op": "greager", // Note: "greager" might be a typo for "greater"
            "values": [
                "1"
            ]
        }
    ]
}
```
注 1：这里的 `mode` 必须大写，否则会出错
注 2：这里的 `values`，必须是一个数组，传 `["多维表"]`，相当于传 `[{ type: 'Text', value: '多维表' }]`，即不传默认帮你补充 Text 类型
注 3：复选框的值，`values: ['0']` 代表否，`value: ['1']` 代表是

## 2. 筛选条件 op 参数说明
-   `"Equals"`: 等于
-   `"NotEqu"`: 不等于
-   `"Greater"`: 大于
-   `"GreaterEqu"`: 大等于
-   `"Less"`: 小于
-   `"LessEqu"`: 小等于
-   `"GreaterEquAndLessEqu"`: 介于（取等）
-   `"LessOrGreater"`: 介于（不取等）
-   `"BeginWith"`: 开头是
-   `"EndWith"`: 结尾是
-   `"Contains"`: 包含
-   `"NotContains"`: 不包含
-   `"Intersected"`: 指定值
-   `"Empty"`: 为空
-   `"NotEmpty"`: 不为空

`values[]`数组内的元素为字符串时，表示文本匹配。
各筛选规则独立地限制了`values`数组内最多允许填写的元素数，当`values`内元素数超过阈值时，该筛选规则将失效。
a.  "为空、不为空"不允许填写元素；
b.  "介于"允许最多填写2个元素；
c.  "指定值"允许填写65535个元素；
d.  其他规则允许最多填写1个元素。

使用指定值（`Intersected`）操作符时，`values` 中填写的值，应该是多维表中实际展示的值。比如筛选"货币"类型的列时，应带上货币符号，比如：
```json
{
    "mode": "AND",
    "criteria": [{
        "field": "货币列",
        "op": "Intersected",
        "values": [
            "$16.99"
        ]
    }]
}
```

## 3. 日期的动态筛选
目前还支持对日期进行动态筛选，此时`values[]`内的元素需以结构体的形式给出：
```python
condition = {
    "mode": "AND",
    "criteria": [
        {
            "field": "日期",
            "op": "Equals",
            "values": [
                {
                    "dynamicType": "lastMonth",
                    "type": "DynamicSimple"
                }
            ]
        }
    ]
}
```
上述示例对应的筛选条件为"等于上一个月"。
要使用日期动态筛选，`values[]`内的结构体需要指定`"type": "DynamicSimple"`，当`"op"`为`"equals"`时，`"dynamicType"`可以为如下的值（大小写不敏感）：
-   `"today"`: 今天
-   `"yesterday"`: 昨天
-   `"tomorrow"`: 明天
-   `"last7Days"`: 最近7天
-   `"last30Days"`: 最近30天
-   `"thisWeek"`: 本周
-   `"lastWeek"`: 上周
-   `"nextWeek"`：下周
-   `"thisMonth"`: 本月
-   `"lastMonth"`: 上月
-   `"nextMonth"`: 次月

当`"op"`为`"greater"`或`"less"`时，`"dynamicType"`只能是昨天、今天或明天。

