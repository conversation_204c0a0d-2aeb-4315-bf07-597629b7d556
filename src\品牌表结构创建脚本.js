/**
 * 模块名称：品牌表结构创建脚本
 * 模块描述：基于现有表结构为新品牌创建相应的表（如画朴、卓芝等）
 * 模块职责：创建指定品牌的表结构
 * 修改时间: 2025-07-17 18:15
 */

// -------- 全局日志记录系统 --------
const GLOBAL_LOG_BUFFER = []; // 用于缓存所有日志条目
const LOG_TABLE_NAME = "脚本执行日志"; // 日志表名称
let SCRIPT_EXECUTION_START_TIME = new Date(); // 记录脚本开始执行的精确时间

//------------------
// 日志记录系统函数
//------------------

/**
 * 辅助函数：将Date对象格式化为 "YYYY/MM/DD HH:MM:SS" 字符串 (WPS DateTime 格式)
 * @param {Date} date - 要格式化的Date对象。
 * @returns {string} 格式化后的日期时间字符串。
 */
function formatDateTimeForWpsTable(date) {
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    date = new Date(); // 如果日期无效，则回退到当前时间
  }
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 辅助函数：将Date对象格式化为 "YYYY-MM-DD HH:MM:SS.mmm" 字符串 (包含毫秒，用于控制台日志)
 */
function formatDateTimeWithMs(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  const milliseconds = String(date.getMilliseconds()).padStart(3, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
}

/**
 * 统一日志记录函数
 * @param {string} level - 日志级别 (INFO, ERROR, WARN, DEBUG)
 * @param {...any} messages - 要记录的消息内容
 */
function logAndBuffer(level, ...messages) {
  const timestamp = formatDateTimeWithMs(new Date());
  const logEntry = `${timestamp} [${level.toUpperCase()}] ${messages.join(
    " "
  )}`;
  GLOBAL_LOG_BUFFER.push(logEntry);

  switch (level.toUpperCase()) {
    case "INFO":
      console.info(logEntry);
      break;
    case "ERROR":
      console.error(logEntry);
      break;
    case "WARN":
      console.warn(logEntry);
      break;
    case "DEBUG":
      console.log(logEntry);
      break;
    default:
      console.log(logEntry);
  }
}

/**
 * 函数名称: writeAirScriptLogsToWpsTable
 *
 * (省略详细注释，与get_table_structure.js中相同)
 */
function writeAirScriptLogsToWpsTable(config) {
  const { logBuffer, logTableName, scriptStartTime } = config;
  const result = { success: false, logRecordId: null, error: null };

  if (
    typeof Application === "undefined" ||
    typeof Application.Sheet === "undefined"
  ) {
    result.error = "Application 或 Application.Sheet 未定义。可能非WPS环境。";
    console.error("[writeAirScriptLogsToWpsTable] " + result.error);
    if (logBuffer && logBuffer.length > 0) {
      console.error(
        "[writeAirScriptLogsToWpsTable] 缓存的日志:\n" + logBuffer.join("\n")
      );
    }
    return result;
  }

  if (!logBuffer || logBuffer.length === 0) {
    result.success = true;
    return result;
  }

  const logContentForTable = logBuffer.join("\n");
  const FIXED_LOG_FIELDS = [
    { name: "执行时间", type: "MultiLineText" },
    { name: "日志内容", type: "MultiLineText" },
  ];

  let logSheetId = null;
  try {
    const sheets = Application.Sheet.GetSheets();
    let existingLogSheet = null;
    if (Array.isArray(sheets)) {
      for (let i = 0; i < sheets.length; i++) {
        if (sheets[i] && String(sheets[i].name) === String(logTableName)) {
          existingLogSheet = sheets[i];
          break;
        }
      }
    }

    if (existingLogSheet) {
      logSheetId = Number(existingLogSheet.id);
      const existingFieldsResult = Application.Field.GetFields({
        SheetId: logSheetId,
      });
      const existingFieldNames = [];
      if (Array.isArray(existingFieldsResult)) {
        for (let i = 0; i < existingFieldsResult.length; i++) {
          if (existingFieldsResult[i] && existingFieldsResult[i].name != null) {
            existingFieldNames.push(String(existingFieldsResult[i].name));
          }
        }
      }

      const fieldsToAdd = [];
      for (let i = 0; i < FIXED_LOG_FIELDS.length; i++) {
        const requiredField = FIXED_LOG_FIELDS[i];
        if (
          !existingFieldNames.some(
            (name) => String(name) === String(requiredField.name)
          )
        ) {
          fieldsToAdd.push({
            name: String(requiredField.name),
            type: String(requiredField.type),
          });
        }
      }

      if (fieldsToAdd.length > 0) {
        try {
          Application.Field.CreateFields({
            SheetId: logSheetId,
            Fields: fieldsToAdd,
          });
        } catch (fieldCreationError) {
          console.error(
            `[错误][writeAirScriptLogsToWpsTable] 字段创建错误: ${fieldCreationError.message}`
          );
        }
      }
    } else {
      try {
        const newSheet = Application.Sheet.CreateSheet({
          Name: String(logTableName),
          Views: [{ name: "所有日志", type: "Grid" }],
          Fields: FIXED_LOG_FIELDS,
        });
        if (newSheet && typeof newSheet.id !== "undefined") {
          logSheetId = Number(newSheet.id);
        } else {
          result.error = `创建日志表失败`;
          return result;
        }
      } catch (sheetCreationError) {
        result.error = `创建日志表错误: ${sheetCreationError.message}`;
        return result;
      }
    }

    if (logSheetId !== null) {
      const executionTimeFormatted = formatDateTimeForWpsTable(scriptStartTime);
      const recordDataFields = {
        执行时间: executionTimeFormatted,
        日志内容: logContentForTable,
      };

      try {
        const createResult = Application.Record.CreateRecords({
          SheetId: logSheetId,
          Records: [{ fields: recordDataFields }],
        });

        if (
          createResult &&
          Array.isArray(createResult) &&
          createResult.length > 0 &&
          typeof createResult[0].id !== "undefined"
        ) {
          result.success = true;
          result.logRecordId = createResult[0].id;
        } else {
          result.error = `日志写入失败`;
        }
      } catch (recordCreationError) {
        result.error = `日志记录创建错误: ${recordCreationError.message}`;
      }
    }
  } catch (e) {
    result.error = `writeAirScriptLogsToWpsTable 意外错误: ${e.message}`;
  }
  return result;
}

//------------------
// 品牌表创建核心函数
//------------------

/**
 * 配置信息
 * 需要根据实际情况修改这些配置
 */
const BRAND_TABLE_CONFIG = {
  // 目标品牌名称（要创建的新品牌）
  targetBrand: "卓芝",

  // 标准表模板定义（基于画朴的表结构）
  standardTables: [
    {
      templateName: "目标管理",
      description: "品牌目标分配和管理表",
      fields: [
        { name: "团队月度总目标", type: "Number" },
        { name: "已分配目标", type: "Number" },
        { name: "剩余可分配", type: "Number" },
        { name: "制衣配额（可编辑）", type: "Number" },
        { name: "毛织配额（可编辑）", type: "Number" },
      ],
    },
    {
      templateName: "完成度",
      description: "品牌任务完成度跟踪表",
      fields: [
        { name: "负责人", type: "MultiLineText" },
        { name: "周", type: "MultiLineText" },
        { name: "本周目标（可编辑）", type: "Number" },
        { name: "制衣目标（可编辑）", type: "Number" },
        { name: "毛织目标（可编辑）", type: "Number" },
        { name: "本周已完成", type: "Number" },
        { name: "周度完成率", type: "Formula" },
        {
          name: "建议等级",
          type: "SingleSelect",
          items: [
            { value: "A级" },
            { value: "B级" },
            { value: "C级" },
            { value: "D级" },
          ],
        },
      ],
    },
    {
      templateName: "个人绩效对比",
      description: "品牌个人绩效对比分析表",
      fields: [
        { name: "负责人", type: "MultiLineText" },
        { name: "W25完成率", type: "MultiLineText" },
      ],
    },
  ],
};

/**
 * 为品牌创建标准表
 *
 * 概述: 基于标准表模板为指定品牌创建业务管理表
 * 详细描述: 使用预定义的表模板，为品牌创建目标管理、完成度、个人绩效对比等标准表
 * 调用的函数: Application.Sheet.CreateSheet()
 * 参数: brandName (string) - 品牌名称
 * 返回值: Array - 创建结果信息数组
 * 修改时间: 2025-07-17 18:30
 */
function createStandardTablesForBrand(brandName) {
  try {
    logAndBuffer("INFO", `开始为品牌 "${brandName}" 创建标准业务表...`);

    const results = [];

    // 检查现有表，避免重复创建
    const existingSheets = Application.Sheet.GetSheets();
    const existingTableNames = [];
    if (Array.isArray(existingSheets)) {
      for (let i = 0; i < existingSheets.length; i++) {
        if (existingSheets[i] && existingSheets[i].name) {
          existingTableNames.push(String(existingSheets[i].name));
        }
      }
    }

    // 为每个标准表模板创建品牌表
    for (let i = 0; i < BRAND_TABLE_CONFIG.standardTables.length; i++) {
      const template = BRAND_TABLE_CONFIG.standardTables[i];

      try {
        const tableName = `「${brandName}」${template.templateName}`;

        // 检查表是否已存在
        if (existingTableNames.includes(tableName)) {
          logAndBuffer("WARN", `表 "${tableName}" 已存在，跳过创建`);
          results.push({
            success: false,
            tableName: tableName,
            reason: "表已存在",
            templateName: template.templateName,
          });
          continue;
        }

        logAndBuffer("INFO", `正在创建表: ${tableName}`);
        logAndBuffer("DEBUG", `表描述: ${template.description}`);
        logAndBuffer("DEBUG", `字段数量: ${template.fields.length}`);

        // 准备字段配置
        const fields = [];
        for (let j = 0; j < template.fields.length; j++) {
          const fieldTemplate = template.fields[j];
          const field = {
            name: fieldTemplate.name,
            type: fieldTemplate.type,
          };

          // 根据字段类型添加特殊配置
          if (fieldTemplate.items) {
            field.items = fieldTemplate.items;
          }
          if (fieldTemplate.max) {
            field.max = fieldTemplate.max;
          }
          if (fieldTemplate.multipleContacts !== undefined) {
            field.multipleContacts = fieldTemplate.multipleContacts;
          }
          if (fieldTemplate.noticeNewContact !== undefined) {
            field.noticeNewContact = fieldTemplate.noticeNewContact;
          }
          if (fieldTemplate.displayText) {
            field.displayText = fieldTemplate.displayText;
          }
          if (fieldTemplate.linkSheet) {
            field.linkSheet = fieldTemplate.linkSheet;
            field.multipleLinks = fieldTemplate.multipleLinks || false;
          }

          fields.push(field);
          logAndBuffer("DEBUG", `准备字段: ${field.name} (${field.type})`);
        }

        // 创建表
        const createParams = {
          Name: tableName,
          Fields: fields,
          Views: [{ name: "表", type: "Grid" }],
        };

        logAndBuffer("DEBUG", `创建表参数准备完成，开始调用API...`);

        const newSheet = Application.Sheet.CreateSheet(createParams);

        if (newSheet && typeof newSheet.id !== "undefined") {
          logAndBuffer(
            "INFO",
            `✓ 成功创建表 "${tableName}" (ID: ${newSheet.id})`
          );
          results.push({
            success: true,
            tableName: tableName,
            tableId: newSheet.id,
            fieldsCount: fields.length,
            templateName: template.templateName,
            description: template.description,
          });
        } else {
          logAndBuffer("ERROR", `✗ 创建表 "${tableName}" 失败`);
          results.push({
            success: false,
            tableName: tableName,
            reason: "创建失败",
            templateName: template.templateName,
          });
        }
      } catch (tableError) {
        logAndBuffer(
          "ERROR",
          `创建表 "${template.templateName}" 时出错: ${tableError.message}`
        );
        results.push({
          success: false,
          tableName: `「${brandName}」${template.templateName}`,
          reason: tableError.message,
          templateName: template.templateName,
        });
      }
    }

    logAndBuffer(
      "INFO",
      `品牌 "${brandName}" 标准表创建完成，共处理 ${results.length} 个表模板`
    );
    return results;
  } catch (error) {
    logAndBuffer("ERROR", `为品牌创建标准表时出错: ${error.message}`);
    return [];
  }
}

// 此函数已删除，改为使用标准表模板创建

/**
 * 执行批量表创建
 *
 * 概述: 为指定品牌批量创建所有必需的标准业务表
 * 详细描述: 基于标准表模板，为目标品牌创建业务管理表
 * 调用的函数: createStandardTablesForBrand()
 * 返回值: object - 批量创建的结果统计
 * 修改时间: 2025-07-17 18:30
 */
function createBrandTables() {
  try {
    logAndBuffer(
      "INFO",
      `开始为品牌 "${BRAND_TABLE_CONFIG.targetBrand}" 创建标准业务表`
    );
    logAndBuffer(
      "INFO",
      `表模板数量: ${BRAND_TABLE_CONFIG.standardTables.length}`
    );

    // 基于标准模板创建品牌表
    const createResults = createStandardTablesForBrand(
      BRAND_TABLE_CONFIG.targetBrand
    );

    if (createResults.length === 0) {
      logAndBuffer("ERROR", "创建表过程中出现错误，未返回任何结果");
      return { success: false, reason: "创建过程出错" };
    }

    const results = {
      total: createResults.length,
      success: 0,
      failed: 0,
      skipped: 0,
      details: [],
    };

    // 统计创建结果
    for (let i = 0; i < createResults.length; i++) {
      const result = createResults[i];
      results.details.push(result);

      if (result.success) {
        results.success++;
        logAndBuffer("INFO", `✓ 成功: ${result.tableName}`);
      } else if (result.reason === "表已存在") {
        results.skipped++;
        logAndBuffer("WARN", `⚠ 跳过: ${result.tableName} (已存在)`);
      } else {
        results.failed++;
        logAndBuffer("ERROR", `✗ 失败: ${result.tableName} (${result.reason})`);
      }
    }

    logAndBuffer(
      "INFO",
      `批量创建完成: 成功 ${results.success} 个，失败 ${results.failed} 个，跳过 ${results.skipped} 个`
    );
    return { success: true, results };
  } catch (error) {
    logAndBuffer("ERROR", `批量创建表时出错: ${error.message}`);
    return { success: false, reason: error.message };
  }
}

//------------------
// 主执行函数
//------------------

// 使用IIFE (Immediately Invoked Function Expression) 结构，按照日志记录规范
(function MainExecutionWrapper() {
  SCRIPT_EXECUTION_START_TIME = new Date();

  logAndBuffer("INFO", "品牌标准表创建脚本开始执行");
  logAndBuffer("INFO", `配置信息:`);
  logAndBuffer("INFO", `  目标品牌: ${BRAND_TABLE_CONFIG.targetBrand}`);
  logAndBuffer(
    "INFO",
    `  表模板: ${BRAND_TABLE_CONFIG.standardTables
      .map((t) => t.templateName)
      .join(", ")}`
  );

  try {
    // 执行主要功能
    const result = createBrandTables();

    if (result.success) {
      logAndBuffer("INFO", "脚本执行成功完成");

      // 显示详细结果
      console.log("\n" + "=".repeat(60));
      console.log("品牌标准表创建结果汇总");
      console.log("=".repeat(60));
      console.log(`目标品牌: ${BRAND_TABLE_CONFIG.targetBrand}`);
      console.log(`总计: ${result.results.total} 个表`);
      console.log(`成功: ${result.results.success} 个`);
      console.log(`失败: ${result.results.failed} 个`);
      console.log(`跳过: ${result.results.skipped} 个`);
      console.log("=".repeat(60));

      for (let i = 0; i < result.results.details.length; i++) {
        const detail = result.results.details[i];
        const status = detail.success
          ? "✓"
          : detail.reason === "表已存在"
          ? "⚠"
          : "✗";
        console.log(`${status} ${detail.tableName}`);
        if (detail.success) {
          console.log(
            `    表ID: ${detail.tableId}, 字段数: ${detail.fieldsCount}, 视图数: ${detail.viewsCount}`
          );
        } else if (detail.reason !== "表已存在") {
          console.log(`    失败原因: ${detail.reason}`);
        }
      }
      console.log("=".repeat(60));
    } else {
      logAndBuffer("ERROR", `脚本执行失败: ${result.reason}`);
    }
  } catch (error) {
    logAndBuffer(
      "ERROR",
      `脚本顶层执行发生致命错误: ${error.message || JSON.stringify(error)}`
    );
    if (error.stack) {
      logAndBuffer("ERROR", `顶层错误堆栈: ${error.stack}`);
    }
  } finally {
    // 调用通用日志函数
    const loggingConfig = {
      logBuffer: GLOBAL_LOG_BUFFER,
      logTableName: LOG_TABLE_NAME,
      scriptStartTime: SCRIPT_EXECUTION_START_TIME,
    };

    const loggingOutcome = writeAirScriptLogsToWpsTable(loggingConfig);

    if (!loggingOutcome.success) {
      console.error(
        "!! 严重错误: 脚本完成时未能将脚本执行日志写入WPS表: " +
          loggingOutcome.error
      );
    } else {
      console.log(
        "✓ 脚本执行日志已成功写入表: " +
          LOG_TABLE_NAME +
          " (记录ID: " +
          loggingOutcome.logRecordId +
          ")"
      );
    }
  }
})();
