# 产品开发计划性管理系统设计方案 (V2)

## 技术架构概述

### 系统分工体系

#### PaaS系统职责

- **业务流程管理**：承载核心业务流程，记录审品、打版等业务操作。
- **原始数据存储**：保存审品表、品牌档案、款系列档案等业务原始数据。
- **核心数据计算**：负责执行基于新方案的全部核心计算逻辑，如基础配额、最终目标等。
- **数据API服务**：提供数据查询接口，供WPS系统调用获取计算结果。

#### WPS系统职责

- **数据纯展示平台**：通过API获取PaaS系统计算好的结果，进行纯粹的数据可视化展示。
- **数据可视化界面**：设计管理看板、个人看板、实时大屏等。
- **用户操作界面**：提供界面供管理层录入“调整配额”等战略性数据。

### 技术栈

- **数据存储**: SQL Server (PaaS系统)
- **数据可视化**: WPS多维表格
- **数据处理与回传**: JavaScript / Python
- **数据同步**: API接口调用

### 系统架构图

```mermaid
graph TD
    subgraph PaaS系统 (核心计算与存储)
        A[业务数据<br/>(审品表, 款系列档案等)]
        B[核心计算引擎<br/>(定时任务)]
        C[团队配额管理表<br/>(存储计算结果)]
        D[API接口服务]
    end

    subgraph WPS系统 (数据展示与操作)
        E[WPS多维表格]
        F[实时竞争大屏]
        G[团队/个人看板]
        H[调整配额录入界面]
    end

    A -- "上月产出、完成率等" --> B
    B -- "计算: 基础配额、最终目标" --> C
    C -- "提供数据" --> D
  
    D -- "拉取数据" --> E
    E -- "渲染" --> F
    E -- "渲染" --> G

    H -- "输入调整配额" --> D
    D -- "更新" --> C

    style B fill:#e1f5fe,stroke:#333,stroke-width:2px
    style H fill:#fff3e0,stroke:#333,stroke-width:2px
```

---

## 数据库扩展设计

### 现有表结构分析

核心业务数据依然来源于PaaS系统中的现有表：

1. **打版申请** (`tb___f_89b7c728b76ade5d`)
2. **审品表** (`tb___f_8b77100808d187dd`) - 核心业务表

   - 包含审品通过状态：`f_17f9acb6930a59ec AS [状态]` (状态="通过"表示审品通过)
   - 包含产品类型：`f_1851ef89d51e7338 AS [产品类型]` (可用于区分制衣/毛织)
   - 包含款式编码：`f_1851f0193c15983f AS [款式编码]` (关联到款系列档案)
   - 包含紧急程度：`f_1821766bbbed849c AS [紧急程度]` (用于标识加急申请)
   - 通过款式编码关联到款系列档案，获取开款人信息进行个人跟踪
3. **品牌档案** (`tb___f_e624233ca743e567`)
4. **品牌档案** (`tb___f_e624233ca743e567`)
5. **品牌档案.产品开发目标** (`tb___f_e624233ca743e567_197f862cf351062f`)
6. **款系列档案** (`tb___f_13dafa2b1cfe13c5`)
7. **款系列档案.产品开发目标** (`tb___f_13dafa2b1cfe13c5_197fe16de1ec2bab`)

### 核心表重构设计：月度绩效管理

我们将直接重构现有的“品牌档案.产品开发目标”子表，以其为核心，承载新方案所需的全部团队月度绩效数据。

- **目标表名**: `品牌档案.产品开发目标` (`tb___f_e624233ca743e567_197f862cf351062f`)
- **用途**: 存储每个团队每月的绩效结果、配额计算过程和最终目标。
- **主键**: `(f_sn, f_index)` 联合主键。

| 字段名称           | 字段ID (真实)          | 数据类型         | 备注                                                             |
| ------------------ | ---------------------- | ---------------- | ---------------------------------------------------------------- |
| f_index            | `f_index`            | `int`          | 行号，与f_sn构成联合主键                                         |
| **生效月份** | `f_197f8636118bbb37` | `varchar(7)`   | 格式 `YYYY-MM`，标识数据归属月份。                             |
| 品牌ID             | `f_sn`               | `varchar(32)`  | 关联品牌档案主表                                                 |
| 上月目标完成率     | `f_197f86383eec4781` | `decimal(5,2)` | 上月最终目标完成率                                               |
| 上月实际产出       | `f_197f863d115f86e2` | `int`          | 统计自审品的上月完成款数                                         |
| 上月绩效状态       | `f_1981264b727f6692` | `varchar(10)`  | "达标" 或 "不达标"                                               |
| 基础配额           | `f_1981264ed988983b` | `int`          | 根据上月完成率自动计算得出的资源**上限**。                 |
| **初始目标** | `f_198172b5df846222` | `int`          | **团队在本月基础配额内自主设定的目标，不得超过基础配额。** |
| 调整配额           | `f_19812653e90a4a21` | `int`          | 管理层人工设定，用于战略调配，默认为0。                          |
| 最终目标           | `f_19812654fa92ae27` | `int`          | `初始目标 + 调整配额`，本月唯一的资源上限和考核指标。          |
| 已提交款数         | `f_19812655f9882aa9` | `int`          | 团队已提交进入开发流程的款系列数量                               |
| 已完成款数         | `f_198126571a0e57ae` | `int`          | 团队已完成（审品通过）的款系列数量                               |
| 制衣配额           | `f_19812658f20e905f` | `int`          | 团队长设定的当月制衣品类目标数                                   |
| 毛织配额           | `f_1981265c932555e8` | `int`          | 团队长设定的当月毛织品类目标数                                   |
| 制衣完成数         | `f_1981265e829d925c` | `int`          | 分类统计：已完成的制衣类款数                                     |
| 毛织完成数         | `f_1981265f6b9b9eb1` | `int`          | 分类统计：已完成的毛织类款数                                     |
| 最后更新时间       | `f_1981266361e8ae5c` | `datetime`     | 记录本条数据最后一次被修改的时间                                 |

### 核心表重构设计：个人周度绩效

我们将直接重构现有的“款系列档案.产品开发目标”子表，用于存储每个负责人（个人）每周由团队长分配的目标，并跟踪其实时表现。

- **目标表名**: `款系列档案.产品开发目标` (`tb___f_13dafa2b1cfe13c5_197fe16de1ec2bab`)
- **主键**: `(f_sn, f_index)` 联合主键。

| 字段名称           | 字段ID (真实)          | 数据类型        | 备注                                                                  |
| ------------------ | ---------------------- | --------------- | --------------------------------------------------------------------- |
| f_index            | `f_index`            | `int`         | 行号，与f_sn构成联合主键                                              |
| 款系列ID           | `f_sn`               | `varchar(32)` | 关联款系列档案主表                                                    |
| **周**       | `f_1981267aa59ee6ba` | `varchar(8)`  | 格式 `YYYY-Www`，例如 `2024-W28`                                  |
| 本周目标款数       | `f_197fe17af8feb9d1` | `int`         | 由团队长在WPS界面分配                                                 |
| 制衣目标           | `f_19812685f9f79f85` | `int`         | 个人本周的制衣目标                                                    |
| 毛织目标           | `f_19812686f0f01247` | `int`         | 个人本周的毛织目标                                                    |
| 本周已完成款数     | `f_197fe185c3ccbabe` | `int`         | 实时分类统计：个人本周完成总数                                        |
| 制衣已完成数       | `f_1981268af889481c` | `int`         | 实时分类统计：个人本周完成的制衣款数                                  |
| 毛织已完成数       | `f_197fe181a5d66b58` | `int`         | 实时分类统计：个人本周完成的毛织款数                                  |
| **建议等级** | `f_197fe18b75df5451` | `varchar(20)` | 系统根据**上周**个人完成率计算得出（优秀/良好/需改进/重点关注） |
| 最后更新时间       | `f_198126914ec69770` | `datetime`    | 记录本条数据最后一次被修改的时间                                      |

---

## 功能模块设计

> **📋 WPS表格实现更新说明**  
> 基于实际开发需求，WPS表格设计已从原理论方案优化为实际可操作的表结构：
> - ✅ 界面拆分化：复杂综合界面拆分为多个专门化小表
> - ✅ 权限UI化：字段名称直接标明操作权限和责任人
> - ✅ 数据类型精细化：使用更适合的WPS字段类型
> - ✅ 团队特化：每个品牌团队拥有独立管理表
> - ✅ 运维支持：新增脚本执行日志表
> - ❌ 移除个人自查看板：简化系统复杂度

### 模块一：月度绩效与配额计算模块 (自动化核心)

- **执行方式**: PaaS系统后台定时任务 (Scheduled Task)
- **执行频率**: 每月1日凌晨 01:00

#### 1.1 计算步骤

1. **触发任务**:
   - 系统定时器在每月1日触发SQL存储过程或脚本。
2. **遍历所有团队**:
   - 脚本自动从 `品牌档案`中获取所有需要计算的团队列表。
3. **获取上月绩效数据 (For each team)**:
   - **获取上月目标 (`f_19812654fa92ae27`)**: 从本表（`品牌档案.产品开发目标`）中，查询该团队上一个月的 `f_19812654fa92ae27` 值。
   - **获取上月实际产出 (`f_197f863d115f86e2`)**: 统计 `审品表`中，该团队上一个月所有状态为“通过”的记录总数。
   - **处理异常**: 如果 `f_19812654fa92ae27`为0或未设置，则 `f_197f86383eec4781`记为0，且绩效状态直接视为“不达标”。
4. **计算并写入核心数据**:
   - **计算上月完成率 (`f_197f86383eec4781`)**: `(f_197f863d115f86e2 / f_19812654fa92ae27) * 100`。如果上月目标无效，则记为0。
   - **判断绩效状态 (`f_1981264b727f6692`)**:
     - **新增规则**: 如果 `f_19812654fa92ae27` (上月最终目标) 为NULL、0或负数，则状态直接为“不达标”。
     - 否则，如果 `f_197f86383eec4781 >= 95%`，则状态为“达标”。
     - 否则，状态为“不达标”。
   - **计算基础配额 (`f_1981264ed988983b`)**:
     - 如果状态为“达标”，`f_1981264ed988983b = f_197f863d115f86e2 * 1.2`。
     - 如果状态为“不达标”，`f_1981264ed988983b = f_197f863d115f86e2 * 1.0`。
   - **计算初始目标 (`f_198172b5df846222`) 和最终目标 (`f_19812654fa92ae27`)**:
     - 在团队和管理层介入前，系统将 `初始目标` 和 `最终目标` 的值都默认设置为 `基础配额` 的值。
5. **存入数据库**:
   - 最终，脚本将为每个团队在 `品牌档案.产品开发目标`表中创建或更新当月的绩效记录。

#### 1.2 SQL实现示例

```sql
-- =================================================================================
-- 模块一：月度绩效与配额计算模块 (自动化核心)
-- 执行频率: 每月1日凌晨 01:00
-- 核心逻辑:
-- 1. 计算所有团队上个月的实际产出。
-- 2. 获取所有团队上个月的最终目标。
-- 3. 根据完成率（产出/目标）判断绩效状态（达标/不达标）。
-- 4. 根据绩效状态和上月产出，计算本月的基础配额。
-- 5. 将计算结果作为新月份的记录插入到目标表中。
-- =================================================================================

-- 增加执行条件判断：只有在每月1号才执行核心计算逻辑。
IF DAY(GETDATE()) = 1
BEGIN

-- 声明变量，用于定义当前计算周期的目标月份和上一个月份
-- 在实际自动化脚本中，这些值应该动态生成。
-- 使用 GETDATE() 动态生成月份，确保脚本在任何时候运行都能计算正确的月份。
DECLARE @目标月份 VARCHAR(7) = FORMAT(GETDATE(), 'yyyy-MM'); -- 目标月份为当前月
DECLARE @上个月份 VARCHAR(7) = FORMAT(DATEADD(month, -1, GETDATE()), 'yyyy-MM'); -- 上一月份
DECLARE @上月第一天 DATE = CONVERT(DATE, @上个月份 + '-01');
DECLARE @上月最后一天 DATE = EOMONTH(@上月第一天);

-- 创建一个临时表，用于存储每个团队上个月的绩效计算过程和结果。
IF OBJECT_ID('tempdb..#团队上月绩效') IS NOT NULL
    DROP TABLE #团队上月绩效;

CREATE TABLE #团队上月绩效 (
    品牌ID NVARCHAR(100) PRIMARY KEY, -- 存储品牌档案的f_sn
    上月产出 INT DEFAULT 0,
    上月目标 INT DEFAULT 1, -- 默认为1，避免除以0的错误
    完成率 DECIMAL(5, 2) DEFAULT 0.00,
    绩效状态 NVARCHAR(20),
    基础配额 INT
);

-- 步骤1: 计算所有团队上个月的【实际产出】
-- 数据来源: 审品表 (tb___f_8b77100808d187dd)
-- 统计逻辑: 修正了关联方式，通过审品表->款系列档案->品牌档案来找到正确的品牌ID。
INSERT INTO #团队上月绩效 (品牌ID, 上月产出)
SELECT
    b.f_sn, -- 最终得到的品牌档案的f_sn
    COUNT(s.f_sn) -- 统计审品记录数
FROM
    tb___f_8b77100808d187dd AS s -- 审品表
INNER JOIN
    tb___f_13dafa2b1cfe13c5 AS ks ON LEFT(s.f_1851f0193c15983f, PATINDEX('%[0-9]%', s.f_1851f0193c15983f) - 1) = ks.f_18721eea65e5e9e7 -- 提取款式编码的英文字母前缀进行关联
INNER JOIN
    tb___f_e624233ca743e567 AS b ON ks.f_191e5a1de2002abe = b.f_sn -- 款系列档案的品牌ID -> 品牌档案主键
WHERE
    s.f_17f9acb6930a59ec = '通过' -- 状态 = 通过
    AND CONVERT(datetime, s.f_1831185a4f3ead59) >= @上月第一天 -- 审品通过时间 >= 上月第一天
    AND CONVERT(datetime, s.f_1831185a4f3ead59) <= @上月最后一天   -- 审品通过时间 <= 上月最后一天
GROUP BY
    b.f_sn;

-- 步骤2: 获取所有团队上个月的【最终目标】
-- 数据来源: 品牌档案.产品开发目标 (tb___f_e624233ca743e567_197f862cf351062f)
UPDATE tmp
SET
    tmp.上月目标 = ISNULL(pt.f_19812654fa92ae27, 0) -- 如果上月没目标, 默认为0，便于后续判断
FROM
    #团队上月绩效 tmp
LEFT JOIN
    tb___f_e624233ca743e567_197f862cf351062f pt ON tmp.品牌ID = pt.f_sn
WHERE
    pt.f_197f8636118bbb37 = @上个月份;

-- 步骤3: 计算【完成率】和【绩效状态】
-- 核心规则更新：如果上月目标为0或不存在，则直接判定为“不达标”，完成率为0。
UPDATE #团队上月绩效
SET
    完成率 = CASE
                WHEN 上月目标 > 0 THEN (CAST(上月产出 AS DECIMAL(10, 2)) / 上月目标)
                ELSE 0
             END,
    绩效状态 = CASE
                    WHEN 上月目标 <= 0 THEN '不达标' -- 规则强化：没有有效目标，直接不达标
                    WHEN (CAST(上月产出 AS DECIMAL(10, 2)) / 上月目标) >= 0.95 THEN '达标'
                    ELSE '不达标'
                END;

-- 步骤4: 计算本月的【基础配额】
UPDATE #团队上月绩效
SET
    基础配额 = CASE
                    WHEN 绩效状态 = '达标' THEN CAST(上月产出 * 1.2 AS INT)
                    ELSE 上月产出
                END;

-- 步骤5: 将计算结果作为新月份的记录，插入到目标表中
MERGE tb___f_e624233ca743e567_197f862cf351062f AS target
USING #团队上月绩效 AS source
ON (target.f_sn = source.品牌ID AND target.f_197f8636118bbb37 = @目标月份)
WHEN MATCHED THEN
    -- 如果目标月份的记录已存在, 则进行更新
    UPDATE SET
        f_197f863d115f86e2 = source.上月产出,
        f_197f86383eec4781 = source.完成率,
        f_1981264b727f6692 = source.绩效状态,
        f_1981264ed988983b = source.基础配额,
        f_198172b5df846222 = source.基础配额, -- 初始目标默认等于基础配额
        f_19812654fa92ae27 = source.基础配额, -- 初始的最终目标等于基础配额
        f_1981266361e8ae5c = CONVERT(VARCHAR(19), GETDATE(), 120)
WHEN NOT MATCHED BY TARGET THEN
    -- 如果目标月份的记录不存在, 则插入新记录
    INSERT (      
        f_sn,
        f_index,
        f_197f8636118bbb37, -- 生效月份
        f_197f863d115f86e2, -- 上月实际产出
        f_197f86383eec4781, -- 上月目标完成率
        f_1981264b727f6692, -- 上月绩效状态
        f_1981264ed988983b, -- 基础配额
        f_198172b5df846222, -- 初始目标
        f_19812653e90a4a21, -- 调整配额
        f_19812654fa92ae27, -- 最终目标
        f_19812655f9882aa9, -- 已提交款数
        f_198126571a0e57ae, -- 已完成款数
        f_1981266361e8ae5c  -- 最后更新时间
    )
    VALUES (      
        source.品牌ID,
        (SELECT ISNULL(MAX(f_index), 0) + 1 FROM tb___f_e624233ca743e567_197f862cf351062f WHERE f_sn = source.品牌ID), -- 动态生成f_index
        @目标月份,
        source.上月产出,
        source.完成率,
        source.绩效状态,
        source.基础配额,
        source.基础配额, -- 初始目标默认等于基础配额
        0, -- 调整配额初始为0
        source.基础配额, -- 最终目标初始等于基础配额
        0, -- 已提交计数初始为0
        0, -- 已完成计数初始为0
        CONVERT(VARCHAR(19), GETDATE(), 120)
    );

-- 清理临时表
DROP TABLE #团队上月绩效;

END;
```

### 模块二：月度目标管理模块 (人工干预)

**模块目标**：为团队和管理层提供一个界面，用于设定团队的 `初始目标`和公司的 `调整配额`。

#### 2.1 操作流程

1. **团队设定初始目标**:

   * **时机**: 每月1号自动计算出 `基础配额`后。
   * **操作者**: 团队负责人 (团队长)。
   * **界面**: WPS界面上会显示系统计算出的 `基础配额`（只读）。团队长在“初始目标”列输入数值。
   * **核心校验**: 系统**必须**校验 `初始目标 <= 基础配额`。若超出，则提示错误，不允许保存。
   * **数据同步**: 保存后，将 `初始目标` (`f_198172b5df846222`) 的值更新到PaaS数据库，并同步更新 `最终目标` (`f_19812654fa92ae27` = `初始目标` + `调整配额`)。
2. **管理层设定调整配额**:

   * **时机**: 团队设定完 `初始目标`后，或在月度任何需要战略调整的时候。
   * **操作者**: 公司管理层。
   * **界面**: 管理层在WPS界面上可以看到各团队的 `基础配额`和 `初始目标`，并在“调整配额”列为单个或多个团队输入数值。
   * **自动计算**: “最终目标”列根据 `初始目标 + 调整配额` 的公式实时更新。
   * **数据同步**: 点击保存后，将 `调整配额` (`f_19812653e90a4a21`) 和计算出的 `最终目标` (`f_19812654fa92ae27`) 的值保存回PaaS数据库。

#### 2.2 WPS表格设计

**表名：** `团队月度目标设定`

**核心功能：**

1. **月份管理**：通过 `月份` (Date) 字段选择要操作的目标月份。
2. **数据显示**：展示各团队的 `基础配额`、`初始目标（团队统筹）`、`调整配额（公司统筹）`和 `最终目标`。
3. **权限管理**：字段名称直接标明操作权限，提升用户体验。
4. **操作入口**：提供 `更新` (Button) 字段作为数据同步的操作入口。

**实际字段结构：**

| 字段名称 | 字段类型 | 权限说明 | 备注 |
| :------- | :------- | :------- | :--- |
| 团队名称 | MultiLineText | 只读 | 品牌团队标识 |
| 基础配额 | Number | 只读 | 系统自动计算结果 |
| 月份 | Date | 可编辑 | 目标月份选择器 |
| 初始目标（团队统筹） | Number | 团队长编辑 | 必须≤基础配额 |
| 调整配额（公司统筹） | Number | 管理层编辑 | 战略调配额度 |
| 最终目标 | Number | 自动计算 | 初始目标+调整配额 |
| 更新 | Button | 全部用户 | 触发数据同步 |

**数据示例：**

| 团队名称 | 基础配额 | 月份 | 初始目标（团队统筹） | 调整配额（公司统筹） | 最终目标 | 更新 |
| :------- | :------- | :--- | :------------------ | :------------------ | :------- | :--- |
| 画朴 | 120 | 2024-07-01 | 110 | 15 | 125 | [按钮] |
| 卓芝 | 100 | 2024-07-01 | 100 | 0 | 100 | [按钮] |
| 素都 | 80 | 2024-07-01 | 70 | 20 | 90 | [按钮] |

#### 2.3 后端逻辑

1. **接收数据**：后端API能区分是 `初始目标`更新请求还是 `调整配额`更新请求。
2. **数据校验**:
   * 对于 `初始目标`更新：必须从数据库读取对应的 `基础配额`，确保 `初始目标 <= 基础配额`。
   * 对于 `调整配额`更新：验证其为非负整数。
3. **更新数据**：
   * 在 `品牌档案.产品开发目标`子表中找到对应记录。
   * 更新传入的字段 (`初始目标`或 `调整配额`)。
   * 重新计算 `最终目标 = 初始目标 + 调整配额`。
   * 将更新后的记录保存到数据库。
4. **返回结果**：向前端返回操作成功的确认信息。

---

### 模块三：实时数据统计模块 (高频更新)

- **执行方式**: PaaS系统后台定时任务
- **执行频率**: 每小时一次

#### 3.1 统计步骤

1. **触发任务**:
   - 系统定时器每小时触发一次统计程序。
2. **遍历当月团队记录**:
   - 程序查询 `品牌档案.产品开发目标`表中，`f_197f8636118bbb37` (生效月份)为当前月份的所有记录。
3. **实时统计团队数据 (For each team)**:
   - **统计已提交 (`f_19812655f9882aa9`)**: 查询 `打版申请表`，统计该团队本月所有已提交（无论状态）的记录总数。
   - **统计已完成 (`f_198126571a0e57ae`)**: 查询 `审品表`，统计该团队本月状态为“通过”的记录总数。
   - **分类统计**: 分别统计 `f_1981265e829d925c` (制衣完成数) 和 `f_1981265f6b9b9eb1` (毛织完成数)。
4. **更新团队数据库**:
   - 将最新的统计数据更新回 `品牌档案.产品开发目标`表中对应的记录。
5. **遍历当周个人记录**:
   - 程序查询 `款系列档案.产品开发目标`表中，`f_1981267aa59ee6ba` (周) 为当前周的所有记录。
6. **实时统计个人数据 (For each individual)**:
   - **统计周度完成 (`f_197fe185c3ccbabe`)**: 查询 `审品表`，统计该负责人本周状态为“通过”的记录总数。
   - **分类统计**: 分别统计个人的 `f_1981268af889481c` (制衣已完成数) 和 `f_197fe181a5d66b58` (毛织已完成数)。
7. **更新个人数据库**:
   - 将最新的个人统计数据更新回 `款系列档案.产品开发目标`表中对应的记录。
8. **计算个人建议等级 (每周初执行)**:
   - 在每周一的定时任务中，额外增加一个步骤：
   - 获取个人**上周**的 `f_197fe17af8feb9d1` (周度目标)和 `f_197fe185c3ccbabe` (周度完成)。
   - 计算出**上周完成率**。
   - 根据业务方案中定义的规则（≥90%为优秀, 80-89%为良好, 60-79%为需改进, <60%为重点关注），判断出建议等级。
   - 将“优秀”、“良好”等文本结果，更新到个人**本周**记录的 `f_197fe18b75df5451` (建议等级)字段中，供团队长参考。

#### 3.2 SQL实现示例

```sql
-- =================================================================================
-- 模块三：实时数据统计模块 (高频更新)
-- 执行频率: 每小时一次
-- 核心逻辑:
-- 1. 实时更新【团队】当月的已提交、已完成、分类完成款数。
-- 2. 实时更新【个人】当前周的已完成、分类完成款数。
-- 3. (在周一执行)根据上周表现，更新【个人】本周的建议等级。
-- =================================================================================

-- 声明变量，用于定义当前计算周期
DECLARE @当前月份 VARCHAR(7) = FORMAT(GETDATE(), 'yyyy-MM');
DECLARE @当前周 VARCHAR(8) = FORMAT(GETDATE(), 'yyyy-') + 'W' + CAST(DATEPART(wk, GETDATE()) AS VARCHAR(2));
DECLARE @今天是周一 BIT = IIF(DATEPART(weekday, GETDATE()) = 1, 1, 0); -- 假设周一为工作周的第一天(周日为1)

-- =================================================================================
-- 第一部分: 更新【团队】月度实时数据
-- =================================================================================

-- 创建一个临时表来存储团队的实时统计数据
IF OBJECT_ID('tempdb..#团队实时统计') IS NOT NULL
    DROP TABLE #团队实时统计;

CREATE TABLE #团队实时统计 (
    品牌ID NVARCHAR(100) PRIMARY KEY, -- 品牌档案f_sn
    已提交款数 INT DEFAULT 0,
    已完成款数 INT DEFAULT 0,
    制衣完成数 INT DEFAULT 0,
    毛织完成数 INT DEFAULT 0
);

-- 步骤1: 统计【已提交】款数 (假设通过打版申请表)
INSERT INTO #团队实时统计 (品牌ID, 已提交款数)
SELECT
    b.f_sn, -- 品牌档案的f_sn
    COUNT(d.f_sn)
FROM
    tb___f_89b7c728b76ade5d d -- 打版申请表
INNER JOIN
    tb___f_e624233ca743e567 b ON d.f_1936c5092154ea06 = b.f_sn 
WHERE
    FORMAT(CONVERT(datetime, d.f_createtime), 'yyyy-MM') = @当前月份
GROUP BY
    b.f_sn;

-- 步骤2: 统计【已完成】及【分类完成】款数
WITH 完成统计 AS (
    SELECT
        b.f_sn AS 品牌ID,
        COUNT(s.f_sn) AS 总完成数,
        SUM(CASE WHEN s.f_1851ef89d51e7338 = '制衣' THEN 1 ELSE 0 END) AS 制衣完成数,
        SUM(CASE WHEN s.f_1851ef89d51e7338 = '毛织' THEN 1 ELSE 0 END) AS 毛织完成数
    FROM
        tb___f_8b77100808d187dd AS s -- 审品表
    INNER JOIN
        tb___f_13dafa2b1cfe13c5 AS ks ON LEFT(s.f_1851f0193c15983f, PATINDEX('%[0-9]%', s.f_1851f0193c15983f) - 1) = ks.f_18721eea65e5e9e7 -- 提取款式编码的英文字母前缀进行关联
    INNER JOIN
        tb___f_e624233ca743e567 AS b ON ks.f_191e5a1de2002abe = b.f_sn -- 品牌ID关联
    WHERE
        s.f_17f9acb6930a59ec = '通过'
        AND FORMAT(CONVERT(datetime, s.f_1831185a4f3ead59), 'yyyy-MM') = @当前月份
    GROUP BY
        b.f_sn
)
UPDATE trs
SET
    trs.已完成款数 = cs.总完成数,
    trs.制衣完成数 = cs.制衣完成数,
    trs.毛织完成数 = cs.毛织完成数
FROM
    #团队实时统计 trs
JOIN
    完成统计 cs ON trs.品牌ID = cs.品牌ID;

-- 步骤3: 将统计结果更新回【团队】目标表
MERGE tb___f_e624233ca743e567_197f862cf351062f AS target
USING #团队实时统计 AS source
ON (target.f_sn = source.品牌ID AND target.f_197f8636118bbb37 = @当前月份)
WHEN MATCHED THEN
    UPDATE SET
        f_19812655f9882aa9 = ISNULL(source.已提交款数, 0),
        f_198126571a0e57ae = ISNULL(source.已完成款数, 0),
        f_1981265e829d925c = ISNULL(source.制衣完成数, 0),
        f_1981265f6b9b9eb1 = ISNULL(source.毛织完成数, 0),
        f_1981266361e8ae5c = CONVERT(VARCHAR(19), GETDATE(), 120);


-- =================================================================================
-- 第二部分: 更新【个人】周度实时数据
-- =================================================================================

-- 创建一个临时表来存储个人的实时统计数据
IF OBJECT_ID('tempdb..#个人实时统计') IS NOT NULL
    DROP TABLE #个人实时统计;

CREATE TABLE #个人实时统计 (
    个人ID NVARCHAR(100) PRIMARY KEY, -- 款系列档案的f_sn
    周度完成数 INT DEFAULT 0,
    周度制衣完成数 INT DEFAULT 0,
    周度毛织完成数 INT DEFAULT 0
);

-- 步骤4: 统计个人【周度完成】及【分类完成】款数
WITH 个人完成统计 AS (
    SELECT
        ks.f_sn AS 个人ID,
        COUNT(s.f_sn) AS 总完成数,
        SUM(CASE WHEN s.f_1851ef89d51e7338 = '制衣' THEN 1 ELSE 0 END) AS 制衣完成数,
        SUM(CASE WHEN s.f_1851ef89d51e7338 = '毛织' THEN 1 ELSE 0 END) AS 毛织完成数
    FROM
        tb___f_8b77100808d187dd AS s -- 审品表
    JOIN
        tb___f_13dafa2b1cfe13c5 AS ks ON LEFT(s.f_1851f0193c15983f, PATINDEX('%[0-9]%', s.f_1851f0193c15983f) - 1) = ks.f_18721eea65e5e9e7 -- 提取款式编码的英文字母前缀进行关联
    WHERE
        s.f_17f9acb6930a59ec = '通过'
        AND FORMAT(CONVERT(datetime, s.f_1831185a4f3ead59), 'yyyy-') + 'W' + CAST(DATEPART(wk, CONVERT(datetime, s.f_1831185a4f3ead59)) AS VARCHAR(2)) = @当前周
    GROUP BY
        ks.f_sn
)
INSERT INTO #个人实时统计 (个人ID, 周度完成数, 周度制衣完成数, 周度毛织完成数)
SELECT 个人ID, 总完成数, 制衣完成数, 毛织完成数 FROM 个人完成统计;

-- 步骤5: 将统计结果更新回【个人】目标表
MERGE tb___f_13dafa2b1cfe13c5_197fe16de1ec2bab AS target
USING #个人实时统计 AS source
ON (target.f_sn = source.个人ID AND target.f_1981267aa59ee6ba = @当前周)
WHEN MATCHED THEN
    UPDATE SET
        f_197fe185c3ccbabe = ISNULL(source.周度完成数, 0),
        f_1981268af889481c = ISNULL(source.周度制衣完成数, 0),
        f_197fe181a5d66b58 = ISNULL(source.周度毛织完成数, 0),
        f_198126914ec69770 = CONVERT(VARCHAR(19), GETDATE(), 120);


-- =================================================================================
-- 第三部分: (仅在周一执行) 更新个人【建议等级】
-- =================================================================================
IF @今天是周一 = 1
BEGIN
    -- 声明上周的变量
    DECLARE @上周 VARCHAR(8) = FORMAT(DATEADD(wk, -1, GETDATE()), 'yyyy-') + 'W' + CAST(DATEPART(wk, DATEADD(wk, -1, GETDATE())) AS VARCHAR(2));

    -- 创建一个临时表来存储上周的表现和本周的建议等级
    IF OBJECT_ID('tempdb..#建议等级计算') IS NOT NULL
        DROP TABLE #建议等级计算;

    CREATE TABLE #建议等级计算 (
        个人ID NVARCHAR(100) PRIMARY KEY,
        建议等级 NVARCHAR(20)
    );

    -- 步骤6: 计算每个人的建议等级
    INSERT INTO #建议等级计算 (个人ID, 建议等级)
    SELECT
        f_sn,
        CASE
            WHEN f_197fe17af8feb9d1 > 0 AND (CAST(f_197fe185c3ccbabe AS DECIMAL(10,2)) / f_197fe17af8feb9d1) >= 0.9 THEN '优秀'
            WHEN f_197fe17af8feb9d1 > 0 AND (CAST(f_197fe185c3ccbabe AS DECIMAL(10,2)) / f_197fe17af8feb9d1) >= 0.8 THEN '良好'
            WHEN f_197fe17af8feb9d1 > 0 AND (CAST(f_197fe185c3ccbabe AS DECIMAL(10,2)) / f_197fe17af8feb9d1) >= 0.6 THEN '需改进'
            ELSE '重点关注'
        END
    FROM
        tb___f_13dafa2b1cfe13c5_197fe16de1ec2bab
    WHERE
        f_1981267aa59ee6ba = @上周;

    -- 步骤7: 将建议等级更新到【本周】的个人记录中
    MERGE tb___f_13dafa2b1cfe13c5_197fe16de1ec2bab AS target
    USING #建议等级计算 AS source
    ON (target.f_sn = source.个人ID AND target.f_1981267aa59ee6ba = @当前周)
    WHEN MATCHED THEN
        UPDATE SET
            f_197fe18b75df5451 = source.建议等级,
            f_198126914ec69770 = CONVERT(VARCHAR(19), GETDATE(), 120);

    -- 清理临时表
    DROP TABLE #建议等级计算;
END;

-- 清理主临时表
DROP TABLE #团队实时统计;
DROP TABLE #个人实时统计;

```

#### 预警机制

- **执行方式**: **由WPS系统在前端完成**。PaaS系统仅提供原始数据，WPS根据原始数据进行计算和渲染。
- **数据来源**: PaaS系统 API，提供 `f_19812655f9882aa9` (已提交款数) 和 `f_19812654fa92ae27` (最终目标)。
- **核心判断逻辑**: WPS前端或其轻量级后端在获取到数据后，通过计算 `已提交款数 / 最终目标` 的比率，实时在界面上渲染不同的视觉预警效果。

| 预警级别           | 触发条件                         | 预警动作 (在WPS看板中展示)                                         |
| :----------------- | :------------------------------- | :----------------------------------------------------------------- |
| **黄色预警** | `已提交款数 / 最终目标 >= 80%` | 相关数据单元格背景变为**黄色**                               |
| **橙色预警** | `已提交款数 / 最终目标 >= 90%` | 相关数据单元格背景变为**橙色**                               |
| **红色预警** | `已提交款数 / 最终目标 >= 95%` | 相关数据单元格背景变为**红色**，可增加**闪烁**效果     |
| **配额耗尽** | `已提交款数 >= 最终目标`       | 相关单元格高亮提示“**配额已耗尽**”，并停止该团队的提交功能 |

### 模块四：个人周度目标管理模块 (人工干预)

- **执行方式**: 团队长在WPS界面手动操作，触发API调用。
- **执行者**: 团队负责人

#### 4.1 操作流程

1. **选择周 (新增)**:
   - 团队长首先在界面顶部的**周选择器**中选择要管理的周（例如 `2024-W28`）。界面将加载并显示该周的数据。
2. **展示数据与可分配额度**:
   - WPS界面通过API拉取团队的 `f_19812654fa92ae27` (月度最终目标)。
   - 同时，界面需要计算并显示该团队**当月剩余未分配给个人的目标额度**。计算方式为：`团队月度f_19812654fa92ae27 - Σ(该团队所有个人已被分配的f_weekly_target总和)`。
3. **手动录入**:
   - 团队长在WPS的个人绩效管理表格中，为团队内的每个成员（或指定成员）填入所选周的**周度目标** (`f_197fe17af8feb9d1`)，以及可选的 `f_19812685f9f79f85` 和 `f_19812686f0f01247`。
4. **前端校验 (可选但建议)**:
   - WPS界面在提交前可进行初步校验，确保个人周度目标的总和不超过团队的剩余可分配额度。
5. **API调用与后台核心校验**:
   - 用户提交分配后，WPS通过API将分配数据（团队、**周**、个人、目标数）发送到PaaS系统。
   - **PaaS系统后台必须进行严格校验**：再次计算该团队所有个人（包括本次提交的）的目标总和，确认其**小于等于**该团队的月度 `f_19812654fa92ae27`。如果校验失败，则向前端返回错误信息，数据库不进行任何操作。
6. **存入数据库**:
   - 校验通过后，系统将分配的数据写入或更新到 `款系列档案.产品开发目标`表中。
   - **f_index生成**: 在执行 `INSERT`操作时，必须为新记录生成 `f_index`。该值应为当前 `f_sn`下已有的最大 `f_index`加一，以确保联合主键的唯一性。例如: `(SELECT ISNULL(MAX(f_index), 0) + 1 FROM tb___f_13dafa2b1cfe13c5_197fe16de1ec2bab WHERE f_sn = '当前个人f_sn')`。

#### 4.2 WPS表格设计

**设计思路**: 将原设计的综合界面拆分为**3个专门化表格**，每个表职责明确，提升操作效率。

**使用角色**: 团队负责人 (团队长)  
**核心功能**: 为团队分配月度品类配额，并为团队成员**按周**分配目标。

##### 表格1：团队配额管理

**表名：** `「{品牌名}」目标管理` (如：`「画朴」目标管理`)

| 字段名称 | 字段类型 | 权限说明 | 备注 |
| :------- | :------- | :------- | :--- |
| 团队月度总目标 | Number | 只读 | 来自PaaS的最终目标 |
| 已分配目标 | Number | 只读 | 所有个人目标的汇总 |
| 剩余可分配 | Number | 只读 | 总目标-已分配目标 |
| 制衣配额（可编辑） | Number | 团队长编辑 | 制衣品类配额 |
| 毛织配额（可编辑） | Number | 团队长编辑 | 毛织品类配额 |

**数据示例：**

| 团队月度总目标 | 已分配目标 | 剩余可分配 | 制衣配额（可编辑） | 毛织配额（可编辑） |
| :------------- | :--------- | :--------- | :----------------- | :----------------- |
| 194 | 150 | 44 | 130 | 64 |

##### 表格2：个人周度目标管理

**表名：** `「{品牌名}」完成度` (如：`「画朴」完成度`)

| 字段名称 | 字段类型 | 权限说明 | 备注 |
| :------- | :------- | :------- | :--- |
| 负责人 | MultiLineText | 只读 | 个人姓名标识 |
| 周 | MultiLineText | 可编辑 | 格式：2024-W28 |
| 本周目标（可编辑） | Number | 团队长编辑 | 个人周度总目标 |
| 制衣目标（可编辑） | Number | 团队长编辑 | 个人制衣目标 |
| 毛织目标（可编辑） | Number | 团队长编辑 | 个人毛织目标 |
| 本周已完成 | Number | 只读 | 系统实时统计 |
| 周度完成率 | Formula | 自动计算 | 已完成/本周目标 |
| 建议等级 | SingleSelect | 只读 | 基于上周表现计算 |

**建议等级选项：** 优秀、良好、需改进、重点关注 (共4个选项)

**数据示例：**

| 负责人 | 周 | 本周目标（可编辑） | 制衣目标（可编辑） | 毛织目标（可编辑） | 本周已完成 | 周度完成率 | 建议等级 |
| :----- | :- | :----------------- | :----------------- | :----------------- | :--------- | :--------- | :------- |
| 张三 | 2024-W28 | 10 | 8 | 2 | 5 | 50% | 良好 |
| 李四 | 2024-W28 | 8 | 4 | 4 | 6 | 75% | 优秀 |

##### 表格3：个人绩效横向对比

**表名：** `「{品牌名}」个人绩效对比` (如：`「画朴」个人绩效对比`)

| 字段名称 | 字段类型 | 权限说明 | 备注 |
| :------- | :------- | :------- | :--- |
| 负责人 | MultiLineText | 只读 | 个人姓名标识 |
| W25完成率 | MultiLineText | 只读 | 历史周完成率展示 |

**说明：** 
- 当前实现为简化版，仅展示W25周数据
- 后续将通过脚本自动扩展更多周数列
- 可通过颜色编码展示绩效状态（🟢优秀、🟡良好、🔴需改进）

##### 核心交互

- 当用户修改“制衣配额”、“毛织配额”或下方个人目标的任意可编辑单元格后，触发相应的API调用，将新数据发送至PaaS系统进行后台校验和更新。后台需校验团队的 `f_19812658f20e905f + f_1981265c932555e8`不能超过 `f_19812654fa92ae27`。
- 点击主表格中的“< 上一周”或“下一周 >”后，主表格和横向对比区的数据会同步刷新到对应的周。

### 模块五：数据可视化模块 (大屏)

- **执行方式**: 仅数据展示，定时刷新。
- **执行者**: 无，面向全员或特定展示区域。

#### WPS表格设计

**表名：** `综合看板`

**使用角色**: 全体员工/管理层  
**核心功能**: 集中、高密度地展示所有团队的核心竞争指标，营造透明、公开的竞争氛围。  
**数据来源**: PaaS系统的 `品牌档案.产品开发目标`

##### 实际字段结构

| 字段名称 | 字段类型 | 数据来源 | 备注 |
| :------- | :------- | :------- | :--- |
| 团队名称 | MultiLineText | 品牌档案 | 品牌团队标识 |
| 上月绩效 | SingleSelect | PaaS计算 | 选项：达标/不达标 |
| 最终目标 | Number | PaaS数据 | 月度最终目标 |
| 已提交 | Number | 实时统计 | 本月已提交款数 |
| 已完成 | Number | 实时统计 | 本月已完成款数 |
| 实时完成率 | Percentage | 自动计算 | 已完成/最终目标 |
| 制衣完成 | Number | 实时统计 | 制衣品类完成数 |
| 毛织完成 | Number | 实时统计 | 毛织品类完成数 |
| 本周目标 | Number | 聚合计算 | 所有个人本周目标汇总 |
| 周进度 | MultiLineText | 计算展示 | 周度进度状态描述 |
| 制衣配额 | Number | 团队设定 | 制衣品类配额 |
| 毛织配额 | Number | 团队设定 | 毛织品类配额 |
| 剩余配额 | Number | 自动计算 | 最终目标-已提交 |

##### 数据示例

| 团队名称 | 上月绩效 | 最终目标 | 已提交 | 已完成 | 实时完成率 | 制衣完成 | 毛织完成 | 本周目标 | 周进度 | 制衣配额 | 毛织配额 | 剩余配额 |
| :------- | :------- | :------- | :----- | :----- | :--------- | :------- | :------- | :------- | :----- | :------- | :------- | :------- |
| 画朴 | 达标 | 194 | 95 | 88 | 45.4% | 62 | 26 | 28 | 🟢85% | 130 | 64 | 99 |
| 卓芝 | 不达标 | 100 | 75 | 65 | 65.0% | 45 | 20 | 25 | 🟡72% | 70 | 30 | 25 |

**预警机制集成：**
- 黄色预警：已提交/最终目标 ≥ 80%
- 橙色预警：已提交/最终目标 ≥ 90%  
- 红色预警：已提交/最终目标 ≥ 95%
- 配额耗尽：已提交 ≥ 最终目标

### 新增表格：系统运维支持

#### 脚本执行日志表

**表名：** `脚本执行日志`

**核心功能**: 记录系统自动化脚本的执行情况，便于运维监控和问题排查。

**实际字段结构：**

| 字段名称 | 字段类型 | 用途说明 |
| :------- | :------- | :------- |
| 执行时间 | MultiLineText | 脚本执行的时间戳 |
| 日志内容 | MultiLineText | 详细的执行日志和状态信息 |

**使用场景：**
- 月度配额计算脚本执行记录
- 实时数据统计脚本运行状态
- 系统错误和异常情况记录
- 数据同步操作的审计追踪

---

### ⚠️ 已移除模块：个人自查看板

**设计决策**: 基于实际需求评估，决定不实现原设计中的"个人自查看板模块"。

**移除原因**:
- 功能重复性：个人绩效信息已在团队管理界面中充分展示
- 简化系统：减少维护复杂度，专注核心管理功能
- 权限简化：避免个人权限管理的复杂性

**替代方案**: 个人可通过团队管理界面查看自己的绩效数据，团队长负责向成员传达绩效信息。

---
