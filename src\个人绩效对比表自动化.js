/**
 * 脚本名称：个人绩效对比表自动化管理
 *
 * 功能描述：自动添加周数列并从完成度表同步个人绩效数据到对比表
 * 数据流向：WPS「品牌名」完成度表 → WPS「品牌名」个人绩效对比表
 * 使用接口：无 (WPS内部数据处理)
 * 时间范围：按周处理数据
 * 执行频率：建议每周执行一次
 *
 * 修改时间: 2025-07-18 18:30
 */

// ===========================================
// 配置区
// ===========================================

const CONFIG = {
  // 品牌配置
  BRAND_NAMES: ["画朴", "卓芝", "素都"], // 支持的品牌名称

  // WPS表名配置
  WPS_SOURCE_TABLE_SUFFIX: "」完成度", // 源表后缀："「品牌名」完成度"
  WPS_TARGET_TABLE_SUFFIX: "」个人绩效对比", // 目标表后缀："「品牌名」个人绩效对比"
  WPS_LOG_TABLE: "脚本执行日志",

  // 字段配置
  COMPLETION_RATE_THRESHOLDS: {
    EXCELLENT: 0.9, // 90%以上为优秀
    GOOD: 0.8, // 80-90%为良好
    NEEDS_IMPROVEMENT: 0.6, // 60-80%为需改进
    // 60%以下为重点关注
  },

  // 显示符号配置
  PERFORMANCE_SYMBOLS: {
    优秀: "🟢",
    良好: "🟡",
    需改进: "🔴",
    重点关注: "⚫",
    未设目标: "⚪",
  },
};

// 全局变量
let logBuffer = [];
const scriptStartTime = new Date();

// ===========================================
// 辅助函数
// ===========================================

/**
 * 获取当前周数
 *
 * 概述: 获取当前周数的YYYY-Www格式字符串（使用ISO 8601标准）
 * 调用的函数: JavaScript内置Date对象方法
 * 返回值: string - 当前周数字符串
 * 修改时间: 2025-07-18 18:30
 */
function getCurrentWeek() {
  const now = new Date();

  // 使用ISO 8601标准计算周数
  // 将日期设置为当前周的周四（ISO周的中间）
  const thursday = new Date(now.getTime());
  thursday.setDate(now.getDate() + 3 - ((now.getDay() + 6) % 7));

  // 获取年份（可能与当前日期的年份不同）
  const year = thursday.getFullYear();

  // 计算该年第一周的周四
  const firstThursday = new Date(year, 0, 4);
  firstThursday.setDate(
    firstThursday.getDate() + 3 - ((firstThursday.getDay() + 6) % 7)
  );

  // 计算周数
  const weekNumber =
    Math.round(
      (thursday.getTime() - firstThursday.getTime()) / (7 * 24 * 60 * 60 * 1000)
    ) + 1;

  return `${year}-W${String(weekNumber).padStart(2, "0")}`;
}

/**
 * 格式化日期时间
 *
 * 概述: 将Date对象格式化为可读字符串
 * 调用的函数: JavaScript内置Date对象方法
 * 参数: date (Date) - 日期对象
 * 返回值: string - 格式化的日期时间字符串
 * 修改时间: 2025-07-18 18:30
 */
function formatDateTime(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 辅助函数：将Date对象格式化为 "YYYY/MM/DD HH:MM:SS" 字符串 (WPS DateTime 格式)
 *
 * 概述: WPS表格专用的日期时间格式化函数
 * 调用的函数: JavaScript内置Date对象方法
 * 参数: date (Date) - 要格式化的Date对象
 * 返回值: string - 格式化后的日期时间字符串
 * 修改时间: 2025-07-18 18:30
 */
function formatDateTimeForWpsTable(date) {
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    date = new Date(); // 如果日期无效，则回退到当前时间
  }
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从0开始
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 计算绩效等级
 *
 * 概述: 根据完成率计算绩效等级
 * 调用的函数: 无
 * 参数:
 *   completedCount (number) - 完成数量
 *   targetCount (number) - 目标数量
 * 返回值: string - 绩效等级
 * 修改时间: 2025-07-18 18:30
 */
function calculatePerformanceLevel(completedCount, targetCount) {
  if (!targetCount || targetCount <= 0) {
    return "未设目标";
  }

  const completionRate = completedCount / targetCount;

  if (completionRate >= CONFIG.COMPLETION_RATE_THRESHOLDS.EXCELLENT) {
    return "优秀";
  } else if (completionRate >= CONFIG.COMPLETION_RATE_THRESHOLDS.GOOD) {
    return "良好";
  } else if (
    completionRate >= CONFIG.COMPLETION_RATE_THRESHOLDS.NEEDS_IMPROVEMENT
  ) {
    return "需改进";
  } else {
    return "重点关注";
  }
}

/**
 * 生成显示内容
 *
 * 概述: 生成包含符号和完成率的显示内容
 * 调用的函数: calculatePerformanceLevel
 * 参数:
 *   completedCount (number) - 完成数量
 *   targetCount (number) - 目标数量
 * 返回值: string - 格式化的显示内容
 * 修改时间: 2025-07-18 18:30
 */
function generateDisplayContent(completedCount, targetCount) {
  const level = calculatePerformanceLevel(completedCount, targetCount);
  const symbol = CONFIG.PERFORMANCE_SYMBOLS[level];

  if (level === "未设目标") {
    return `${symbol}未设目标`;
  }

  const completionRate = Math.round((completedCount / targetCount) * 100);
  return `${symbol}${completionRate}%`;
}

/**
 * 标准日志记录函数：写入AirScript执行日志到WPS表格
 *
 * 概述: 将AirScript的执行日志写入指定的WPS多维数据表（标准版）
 * 详细描述:
 *   1. 检查指定的日志表是否存在
 *   2. 如果日志表不存在，则创建包含两个固定字段的新表
 *   3. 如果日志表存在，则检查是否包含必需的两个字段
 *   4. 如果有必需的字段缺失，则尝试在日志表中创建这些字段
 *   5. 将传入的日志缓冲内容和脚本执行开始时间写入日志表的新记录中
 * 调用的函数:
 *   - Application.Sheet.GetSheets
 *   - Application.Sheet.CreateSheet
 *   - Application.Field.GetFields
 *   - Application.Field.CreateFields
 *   - Application.Record.CreateRecords
 *   - formatDateTimeForWpsTable
 * 参数: config (object) - 日志写入配置对象
 * 返回值: object - 包含执行状态的对象
 * 修改时间: 2025-07-18 18:30
 */
function writeAirScriptLogsToWpsTable(config) {
  // 参数解构
  const { logBuffer, logTableName, scriptStartTime } = config;

  const result = { success: false, logRecordId: null, error: null };

  if (
    typeof Application === "undefined" ||
    typeof Application.Sheet === "undefined"
  ) {
    result.error = "Application 或 Application.Sheet 未定义。可能非WPS环境。";
    console.error("[writeAirScriptLogsToWpsTable] " + result.error);
    if (logBuffer && logBuffer.length > 0) {
      console.error(
        "[writeAirScriptLogsToWpsTable] 缓存的日志:\\n" + logBuffer.join("\\n")
      );
    }
    return result;
  }

  if (!logBuffer || logBuffer.length === 0) {
    result.success = true; // 认为操作成功，因为没有日志需要写
    return result;
  }

  const logContentForTable = logBuffer.join("\\n");

  // 固定的字段定义
  const FIXED_LOG_FIELDS = [
    { name: "执行时间", type: "MultiLineText" },
    { name: "日志内容", type: "MultiLineText" },
  ];

  let logSheetId = null;
  try {
    const sheets = Application.Sheet.GetSheets();
    let existingLogSheet = null;
    if (Array.isArray(sheets)) {
      for (let i = 0; i < sheets.length; i++) {
        if (sheets[i] && String(sheets[i].name) === String(logTableName)) {
          existingLogSheet = sheets[i];
          break;
        }
      }
    }

    if (existingLogSheet) {
      logSheetId = Number(existingLogSheet.id);

      const existingFieldsResult = Application.Field.GetFields({
        SheetId: logSheetId,
      });
      const existingFieldNames = [];
      if (Array.isArray(existingFieldsResult)) {
        for (let i = 0; i < existingFieldsResult.length; i++) {
          if (existingFieldsResult[i] && existingFieldsResult[i].name != null) {
            existingFieldNames.push(String(existingFieldsResult[i].name));
          }
        }
      }

      const fieldsToAdd = [];
      for (let i = 0; i < FIXED_LOG_FIELDS.length; i++) {
        const requiredField = FIXED_LOG_FIELDS[i];
        if (
          !existingFieldNames.some(
            (name) => String(name) === String(requiredField.name)
          )
        ) {
          fieldsToAdd.push({
            name: String(requiredField.name),
            type: String(requiredField.type),
          });
        }
      }

      if (fieldsToAdd.length > 0) {
        try {
          const createFieldsResult = Application.Field.CreateFields({
            SheetId: logSheetId,
            Fields: fieldsToAdd,
          });
          if (
            !createFieldsResult ||
            !Array.isArray(createFieldsResult) ||
            createFieldsResult.length !== fieldsToAdd.length ||
            createFieldsResult.some((f) => !f || typeof f.id === "undefined")
          ) {
            console.error(
              `[错误][writeAirScriptLogsToWpsTable] 未能向 '${logTableName}' 添加部分/全部缺失字段. API响应: ${JSON.stringify(
                createFieldsResult
              )}`
            );
          }
        } catch (fieldCreationError) {
          console.error(
            `[错误][writeAirScriptLogsToWpsTable] 在为 '${logTableName}' 执行 Application.Field.CreateFields 时发生错误: ${
              fieldCreationError.message || JSON.stringify(fieldCreationError)
            }`
          );
        }
      }
    } else {
      try {
        const newSheet = Application.Sheet.CreateSheet({
          Name: String(logTableName),
          Views: [{ name: "所有日志", type: "Grid" }],
          Fields: FIXED_LOG_FIELDS,
        });
        if (newSheet && typeof newSheet.id !== "undefined") {
          logSheetId = Number(newSheet.id);
        } else {
          result.error = `创建日志表 '${logTableName}' 失败. API响应: ${JSON.stringify(
            newSheet
          )}`;
          console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
          return result;
        }
      } catch (sheetCreationError) {
        result.error = `在为 '${logTableName}' 执行 Application.Sheet.CreateSheet 时发生错误: ${
          sheetCreationError.message || JSON.stringify(sheetCreationError)
        }`;
        console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
        return result;
      }
    }

    if (logSheetId !== null) {
      const executionTimeFormatted = formatDateTimeForWpsTable(scriptStartTime);
      const recordDataFields = {
        执行时间: executionTimeFormatted,
        日志内容: logContentForTable,
      };

      try {
        const createRecordParams = {
          SheetId: logSheetId,
          Records: [{ fields: recordDataFields }],
        };
        const createResult =
          Application.Record.CreateRecords(createRecordParams);

        if (
          createResult &&
          Array.isArray(createResult) &&
          createResult.length > 0 &&
          typeof createResult[0].id !== "undefined"
        ) {
          result.success = true;
          result.logRecordId = createResult[0].id;
        } else {
          result.error = `未能将日志写入表 '${logTableName}'. API响应: ${JSON.stringify(
            createResult
          )}`;
          console.error(
            "[错误][writeAirScriptLogsToWpsTable] " +
              result.error +
              " 数据: " +
              JSON.stringify(recordDataFields)
          );
        }
      } catch (recordCreationError) {
        result.error = `在为 '${logTableName}' 执行 Application.Record.CreateRecords 时发生错误: ${
          recordCreationError.message || JSON.stringify(recordCreationError)
        }`;
        console.error(
          "[错误][writeAirScriptLogsToWpsTable] " +
            result.error +
            " 数据: " +
            JSON.stringify(recordDataFields)
        );
      }
    } else {
      result.error = "日志表的logSheetId为空，无法写入日志。";
      console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
    }
  } catch (e) {
    result.error = `在 writeAirScriptLogsToWpsTable 中发生意外错误: ${
      e.message || JSON.stringify(e)
    }`;
    console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
    if (e.stack)
      console.error(
        "[错误][writeAirScriptLogsToWpsTable] 错误堆栈: " + e.stack
      );
  }
  return result;
}

// ===========================================
// 主要函数
// ===========================================

/**
 * 查找指定品牌的表格
 *
 * 概述: 根据品牌名称和表格后缀查找WPS表格
 * 调用的函数: Application.Sheet.GetSheets
 * 参数:
 *   brandName (string) - 品牌名称
 *   tableSuffix (string) - 表格后缀
 * 返回值: object|null - 找到的表格对象或null
 * 修改时间: 2025-07-18 18:30
 */
function findBrandTable(brandName, tableSuffix) {
  try {
    const targetTableName = `「${brandName}${tableSuffix}`;
    const sheets = Application.Sheet.GetSheets();

    for (let i = 0; i < sheets.length; i++) {
      if (String(sheets[i].name).trim() === targetTableName) {
        return sheets[i];
      }
    }

    return null;
  } catch (error) {
    logBuffer.push(`[ERROR] 查找表格失败: ${error.message}`);
    return null;
  }
}

/**
 * 获取完成度数据
 *
 * 概述: 从完成度表获取指定周的个人绩效数据
 * 调用的函数:
 *   - findBrandTable
 *   - Application.Record.GetRecords
 * 参数:
 *   brandName (string) - 品牌名称
 *   targetWeek (string) - 目标周数
 * 返回值: array - 完成度数据数组
 * 修改时间: 2025-07-18 18:30
 */
function getCompletionData(brandName, targetWeek) {
  try {
    const sourceSheet = findBrandTable(
      brandName,
      CONFIG.WPS_SOURCE_TABLE_SUFFIX
    );

    if (!sourceSheet) {
      logBuffer.push(`[WARN] 找不到品牌 ${brandName} 的完成度表`);
      return [];
    }

    const records = Application.Record.GetRecords({
      SheetId: Number(sourceSheet.id),
    });

    const completionData = [];

    for (let i = 0; i < records.length; i++) {
      const record = records[i];
      const recordWeek = String(record.fields["周"] || "").trim();

      if (recordWeek === targetWeek) {
        completionData.push({
          负责人: String(record.fields["负责人"] || ""),
          "本周目标（可编辑）": Number(
            record.fields["本周目标（可编辑）"] || 0
          ),
          本周已完成: Number(record.fields["本周已完成"] || 0),
        });
      }
    }

    logBuffer.push(
      `[INFO] 从 ${brandName} 完成度表获取到 ${completionData.length} 条 ${targetWeek} 的数据`
    );
    return completionData;
  } catch (error) {
    logBuffer.push(`[ERROR] 获取完成度数据失败: ${error.message}`);
    return [];
  }
}

/**
 * 确保对比表存在所需的周数列
 *
 * 概述: 检查并创建对比表中的周数列
 * 调用的函数:
 *   - Application.Field.GetFields
 *   - Application.Field.CreateFields
 * 参数:
 *   targetSheet (object) - 目标表格对象
 *   weekFieldName (string) - 周数字段名
 * 返回值: boolean - 是否成功确保字段存在
 * 修改时间: 2025-07-18 18:30
 */
function ensureWeekFieldExists(targetSheet, weekFieldName) {
  try {
    // 获取现有字段
    const existingFields = Application.Field.GetFields({
      SheetId: Number(targetSheet.id),
    });

    // 检查字段是否已存在
    for (let i = 0; i < existingFields.length; i++) {
      if (String(existingFields[i].name) === weekFieldName) {
        logBuffer.push(`[INFO] 字段 ${weekFieldName} 已存在`);
        return true;
      }
    }

    // 创建新字段
    const newField = {
      name: weekFieldName,
      type: "MultiLineText",
    };

    const createResult = Application.Field.CreateFields({
      SheetId: Number(targetSheet.id),
      Fields: [newField],
    });

    if (createResult && createResult.length > 0) {
      logBuffer.push(`[INFO] 成功创建字段: ${weekFieldName}`);
      return true;
    } else {
      logBuffer.push(`[ERROR] 创建字段失败: ${weekFieldName}`);
      return false;
    }
  } catch (error) {
    logBuffer.push(`[ERROR] 确保字段存在时失败: ${error.message}`);
    return false;
  }
}

/**
 * 更新对比表数据
 *
 * 概述: 将完成度数据同步到个人绩效对比表
 * 调用的函数:
 *   - findBrandTable
 *   - ensureWeekFieldExists
 *   - Application.Record.GetRecords
 *   - Application.Record.UpdateRecords
 *   - Application.Record.CreateRecords
 *   - generateDisplayContent
 * 参数:
 *   brandName (string) - 品牌名称
 *   targetWeek (string) - 目标周数
 *   completionData (array) - 完成度数据
 * 修改时间: 2025-07-18 18:30
 */
function updateComparisonTable(brandName, targetWeek, completionData) {
  try {
    let targetSheet = findBrandTable(brandName, CONFIG.WPS_TARGET_TABLE_SUFFIX);

    // 如果对比表不存在，创建新表
    if (!targetSheet) {
      logBuffer.push(
        `[INFO] 创建新的个人绩效对比表: 「${brandName}」个人绩效对比`
      );

      const newSheet = Application.Sheet.CreateSheet({
        Name: `「${brandName}」个人绩效对比`,
        Views: [{ name: "表", type: "Grid" }],
        Fields: [{ name: "负责人", type: "MultiLineText" }],
      });

      targetSheet = newSheet;
      logBuffer.push(`[INFO] 成功创建表格，ID: ${targetSheet.id}`);
    }

    // 确保周数列存在
    const weekFieldName = `${targetWeek}完成率`;
    if (!ensureWeekFieldExists(targetSheet, weekFieldName)) {
      return false;
    }

    // 获取现有记录
    const existingRecords = Application.Record.GetRecords({
      SheetId: Number(targetSheet.id),
    });

    const recordsToUpdate = [];
    const recordsToCreate = [];

    for (let i = 0; i < completionData.length; i++) {
      const data = completionData[i];

      // 查找是否存在相同负责人的记录
      let existingRecord = null;
      for (let j = 0; j < existingRecords.length; j++) {
        const existingPersonName = String(
          existingRecords[j].fields["负责人"] || ""
        ).trim();
        const dataPersonName = String(data.负责人 || "").trim();

        if (existingPersonName === dataPersonName) {
          existingRecord = existingRecords[j];
          break;
        }
      }

      // 生成显示内容
      const displayContent = generateDisplayContent(
        data.本周已完成,
        data["本周目标（可编辑）"]
      );

      if (existingRecord) {
        // 更新现有记录
        recordsToUpdate.push({
          recordId: existingRecord.id,
          fields: {
            [weekFieldName]: displayContent,
          },
        });
      } else {
        // 创建新记录
        recordsToCreate.push({
          fields: {
            负责人: data.负责人,
            [weekFieldName]: displayContent,
          },
        });
      }
    }

    // 执行更新操作
    if (recordsToUpdate.length > 0) {
      Application.Record.UpdateRecords({
        SheetId: Number(targetSheet.id),
        Records: recordsToUpdate,
      });
      logBuffer.push(`[INFO] 更新了 ${recordsToUpdate.length} 条现有记录`);
    }

    // 执行创建操作
    if (recordsToCreate.length > 0) {
      Application.Record.CreateRecords({
        SheetId: Number(targetSheet.id),
        Records: recordsToCreate,
      });
      logBuffer.push(`[INFO] 创建了 ${recordsToCreate.length} 条新记录`);
    }

    logBuffer.push(
      `[INFO] ${brandName} 个人绩效对比表 ${targetWeek} 数据更新完成`
    );
    return true;
  } catch (error) {
    logBuffer.push(`[ERROR] 更新对比表失败: ${error.message}`);
    return false;
  }
}

/**
 * 处理单个品牌的绩效对比
 *
 * 概述: 处理指定品牌的个人绩效对比表自动化
 * 调用的函数:
 *   - getCompletionData
 *   - updateComparisonTable
 * 参数:
 *   brandName (string) - 品牌名称
 *   targetWeek (string) - 目标周数
 * 返回值: boolean - 是否处理成功
 * 修改时间: 2025-07-18 18:30
 */
function processBrandPerformance(brandName, targetWeek) {
  try {
    logBuffer.push(`[INFO] 开始处理品牌: ${brandName}`);

    // 1. 获取完成度数据
    const completionData = getCompletionData(brandName, targetWeek);

    if (completionData.length === 0) {
      logBuffer.push(
        `[WARN] 品牌 ${brandName} 没有 ${targetWeek} 的完成度数据`
      );
      return true; // 不视为错误，继续处理其他品牌
    }

    // 2. 更新对比表
    const updateSuccess = updateComparisonTable(
      brandName,
      targetWeek,
      completionData
    );

    if (updateSuccess) {
      logBuffer.push(`[INFO] 品牌 ${brandName} 处理完成`);
      return true;
    } else {
      logBuffer.push(`[ERROR] 品牌 ${brandName} 处理失败`);
      return false;
    }
  } catch (error) {
    logBuffer.push(
      `[ERROR] 处理品牌 ${brandName} 时发生异常: ${error.message}`
    );
    return false;
  }
}

/**
 * 主函数：执行个人绩效对比表自动化管理
 *
 * 概述: 脚本的主要执行逻辑
 * 调用的函数:
 *   - getCurrentWeek
 *   - processBrandPerformance
 *   - writeAirScriptLogsToWpsTable
 * 修改时间: 2025-07-18 18:30
 */
function main() {
  try {
    logBuffer.push(
      `[INFO] ${formatDateTime(new Date())} 开始执行个人绩效对比表自动化管理`
    );

    // 1. 获取当前周数
    const currentWeek = getCurrentWeek();
    logBuffer.push(`[INFO] 当前处理周数: ${currentWeek}`);

    // 2. 处理所有品牌
    let totalBrands = CONFIG.BRAND_NAMES.length;
    let successCount = 0;

    for (let i = 0; i < CONFIG.BRAND_NAMES.length; i++) {
      const brandName = CONFIG.BRAND_NAMES[i];

      if (processBrandPerformance(brandName, currentWeek)) {
        successCount++;
      }
    }

    // 3. 汇总处理结果
    logBuffer.push(
      `[INFO] 处理完成 - 总品牌数: ${totalBrands}, 成功: ${successCount}, 失败: ${
        totalBrands - successCount
      }`
    );

    if (successCount === totalBrands) {
      logBuffer.push(`[INFO] 脚本执行成功完成`);
    } else {
      logBuffer.push(`[WARN] 脚本执行完成，但存在部分失败`);
    }
  } catch (error) {
    logBuffer.push(`[ERROR] 脚本执行失败: ${error.message}`);
    console.error(`脚本执行失败: ${error.message}`);
  } finally {
    // 记录执行日志到WPS表格
    const loggingConfig = {
      logBuffer: logBuffer,
      logTableName: CONFIG.WPS_LOG_TABLE,
      scriptStartTime: scriptStartTime,
    };

    const loggingResult = writeAirScriptLogsToWpsTable(loggingConfig);

    if (!loggingResult.success) {
      console.error(
        "严重错误: 脚本完成时未能将执行日志写入WPS表: " + loggingResult.error
      );
    }
  }
}

// 执行主函数
main();
