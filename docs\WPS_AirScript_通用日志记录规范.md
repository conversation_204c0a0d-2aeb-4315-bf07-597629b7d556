# WPS AirScript 通用日志记录规范

## 1. 概述

本规范旨在为WPS AirScript项目提供一套简单的、可复用的日志记录机制。通过使用本文档中定义的通用日志函数 `writeAirScriptLogsToWpsTable`，开发者可以方便地将脚本执行过程中的详细日志信息持久化到指定的WPS多维数据表中。

该日志系统支持：
- 自动创建日志表（如果不存在）。
- 自动为日志表添加必要的字段（如果缺失）。
- 固定使用两个字段："执行时间"和"日志内容"。
- 记录详细的日志条目，包括时间戳和消息。

## 2. 通用日志函数代码

以下是推荐在所有WPS AirScript项目中使用的通用日志记录函数及其辅助函数。

```javascript
/**
 * 辅助函数：将Date对象格式化为 "YYYY/MM/DD HH:MM:SS" 字符串 (WPS DateTime 格式)
 * @param {Date} date - 要格式化的Date对象。
 * @returns {string} 格式化后的日期时间字符串。
 */
function formatDateTimeForWpsTable(date) {
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    date = new Date(); // 如果日期无效，则回退到当前时间
  }
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从0开始
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * @moduleName 通用WPS多维表日志记录模块（简化版）
 * @moduleDescription 提供一个标准函数，用于将脚本执行日志写入指定的WPS多维表。
 *                  功能包括：自动创建日志表（如果不存在）、自动添加缺失的日志字段、写入日志记录。
 *                  固定使用两个字段："执行时间"和"日志内容"。
 * @version 2.0.0
 * @modificationDate 2025-01-27 10:00 
 */

/**
 * 函数名称: writeAirScriptLogsToWpsTable
 *
 * 概述: 将AirScript的执行日志写入指定的WPS多维数据表（简化版）。
 * 详细描述:
 *   1. 检查指定的日志表是否存在。
 *   2. 如果日志表不存在，则创建包含两个固定字段的新表。
 *   3. 如果日志表存在，则检查是否包含必需的两个字段。
 *   4. 如果有必需的字段缺失，则尝试在日志表中创建这些字段。
 *   5. 将传入的日志缓冲内容和脚本执行开始时间写入日志表的新记录中。
 *   注意: 此函数依赖WPS AirScript环境中的 `Application` 全局对象。
 *
 * @param {object} config - 日志写入配置对象。
 * @param {string[]} config.logBuffer - 包含日志条目（字符串）的数组。通常是脚本中维护的全局日志数组。
 * @param {string} config.logTableName - 要写入日志的目标多维表的名称。
 * @param {Date} config.scriptStartTime - 主脚本开始执行的时间 (Date对象)。
 *
 * @returns {object} result - 包含执行状态的对象。
 * @returns {boolean} result.success - 日志是否成功写入。
 * @returns {string|null} result.logRecordId - 如果成功，则为新创建的日志记录的ID；否则为null。
 * @returns {string|null} result.error - 如果失败，则为错误消息。
 * 修改时间: 2025-01-27 10:00
 */
function writeAirScriptLogsToWpsTable(config) {
  // 参数解构
  const { logBuffer, logTableName, scriptStartTime } = config;

  const result = { success: false, logRecordId: null, error: null };

  if (typeof Application === "undefined" || typeof Application.Sheet === "undefined") {
    result.error = "Application 或 Application.Sheet 未定义。可能非WPS环境。";
    console.error("[writeAirScriptLogsToWpsTable] " + result.error);
    if (logBuffer && logBuffer.length > 0) {
      console.error("[writeAirScriptLogsToWpsTable] 缓存的日志:\\n" + logBuffer.join("\\n"));
    }
    return result;
  }

  if (!logBuffer || logBuffer.length === 0) {
    result.success = true; // 认为操作成功，因为没有日志需要写
    return result;
  }

  const logContentForTable = logBuffer.join("\\n");
  
  // 固定的字段定义
  const FIXED_LOG_FIELDS = [
    { name: "执行时间", type: "MultiLineText" },
    { name: "日志内容", type: "MultiLineText" }
  ];

  let logSheetId = null;
  try {
    const sheets = Application.Sheet.GetSheets();
    let existingLogSheet = null;
    if (Array.isArray(sheets)) {
      for (let i = 0; i < sheets.length; i++) {
        if (sheets[i] && String(sheets[i].name) === String(logTableName)) {
          existingLogSheet = sheets[i];
          break;
        }
      }
    }

    if (existingLogSheet) {
      logSheetId = Number(existingLogSheet.id);

      const existingFieldsResult = Application.Field.GetFields({ SheetId: logSheetId });
      const existingFieldNames = [];
      if (Array.isArray(existingFieldsResult)) {
        for (let i = 0; i < existingFieldsResult.length; i++) {
          if (existingFieldsResult[i] && existingFieldsResult[i].name != null) {
            existingFieldNames.push(String(existingFieldsResult[i].name));
          }
        }
      }

      const fieldsToAdd = [];
      for (let i = 0; i < FIXED_LOG_FIELDS.length; i++) {
        const requiredField = FIXED_LOG_FIELDS[i];
        if (!existingFieldNames.some(name => String(name) === String(requiredField.name))) {
          fieldsToAdd.push({ name: String(requiredField.name), type: String(requiredField.type) });
        }
      }

      if (fieldsToAdd.length > 0) {
        try {
          const createFieldsResult = Application.Field.CreateFields({
            SheetId: logSheetId,
            Fields: fieldsToAdd,
          });
          if (!createFieldsResult || !Array.isArray(createFieldsResult) || createFieldsResult.length !== fieldsToAdd.length || createFieldsResult.some(f => !f || typeof f.id === \'undefined\')) {
             console.error(`[错误][writeAirScriptLogsToWpsTable] 未能向 \'${logTableName}\' 添加部分/全部缺失字段. API响应: ${JSON.stringify(createFieldsResult)}`);
          }
        } catch (fieldCreationError) {
          console.error(`[错误][writeAirScriptLogsToWpsTable] 在为 \'${logTableName}\' 执行 Application.Field.CreateFields 时发生错误: ${fieldCreationError.message || JSON.stringify(fieldCreationError)}`);
        }
      }
    } else {
      try {
        const newSheet = Application.Sheet.CreateSheet({
          Name: String(logTableName),
          Views: [{ name: "所有日志", type: "Grid" }],
          Fields: FIXED_LOG_FIELDS,
        });
        if (newSheet && typeof newSheet.id !== "undefined") {
          logSheetId = Number(newSheet.id);
        } else {
          result.error = `创建日志表 \'${logTableName}\' 失败. API响应: ${JSON.stringify(newSheet)}`;
          console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
          return result;
        }
      } catch (sheetCreationError) {
        result.error = `在为 \'${logTableName}\' 执行 Application.Sheet.CreateSheet 时发生错误: ${sheetCreationError.message || JSON.stringify(sheetCreationError)}`;
        console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
        return result;
      }
    }

    if (logSheetId !== null) {
      const executionTimeFormatted = formatDateTimeForWpsTable(scriptStartTime);
      const recordDataFields = {
        "执行时间": executionTimeFormatted,
        "日志内容": logContentForTable
      };
      
      try {
        const createRecordParams = {
          SheetId: logSheetId,
          Records: [{ fields: recordDataFields }],
        };
        const createResult = Application.Record.CreateRecords(createRecordParams);

        if (createResult && Array.isArray(createResult) && createResult.length > 0 && typeof createResult[0].id !== "undefined") {
          result.success = true;
          result.logRecordId = createResult[0].id;
        } else {
          result.error = `未能将日志写入表 \'${logTableName}\'. API响应: ${JSON.stringify(createResult)}`;
          console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error + " 数据: " + JSON.stringify(recordDataFields));
        }
      } catch (recordCreationError) {
        result.error = `在为 \'${logTableName}\' 执行 Application.Record.CreateRecords 时发生错误: ${recordCreationError.message || JSON.stringify(recordCreationError)}`;
        console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error  + " 数据: " + JSON.stringify(recordDataFields));
      }
    } else {
      result.error = "日志表的logSheetId为空，无法写入日志。";
      console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
    }
  } catch (e) {
    result.error = `在 writeAirScriptLogsToWpsTable 中发生意外错误: ${e.message || JSON.stringify(e)}`;
    console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
    if (e.stack) console.error("[错误][writeAirScriptLogsToWpsTable] 错误堆栈: " + e.stack);
  }
  return result;
}
```

## 3. 使用方法

### 3.1. 准备工作

1. **复制函数**：将上述 `formatDateTimeForWpsTable` 和 `writeAirScriptLogsToWpsTable` 函数复制到您的 AirScript 脚本文件的合适位置。

2. **全局日志变量**：在您的脚本顶部定义以下全局变量：

    ```javascript
    // -------- 全局日志记录系统 --------
    const GLOBAL_LOG_BUFFER = []; // 用于缓存所有日志条目
    const LOG_TABLE_NAME = "脚本执行日志"; // 日志表名称
    let SCRIPT_EXECUTION_START_TIME = new Date(); // 记录脚本开始执行的精确时间
    ```

3. **日志记录函数 `logAndBuffer`**：

    ```javascript
    /**
     * 辅助函数：将Date对象格式化为 "YYYY-MM-DD HH:MM:SS.mmm" 字符串 (包含毫秒，用于控制台日志)
     */
    function formatDateTimeWithMs(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      const milliseconds = String(date.getMilliseconds()).padStart(3, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
    }

    /**
     * 统一日志记录函数
     * @param {string} level - 日志级别 (INFO, ERROR, WARN, DEBUG)
     * @param {...any} messages - 要记录的消息内容
     */
    function logAndBuffer(level, ...messages) {
      const timestamp = formatDateTimeWithMs(new Date());
      const logEntry = `${timestamp} [${level.toUpperCase()}] ${messages.join(" ")}`;
      GLOBAL_LOG_BUFFER.push(logEntry);

      switch (level.toUpperCase()) {
        case "INFO": console.info(logEntry); break;
        case "ERROR": console.error(logEntry); break;
        case "WARN": console.warn(logEntry); break;
        case "DEBUG": console.log(logEntry); break;
        default: console.log(logEntry);
      }
    }
    ```

### 3.2. 在脚本中记录日志

在您的脚本逻辑中，使用 `logAndBuffer` 函数记录需要的日志信息：

```javascript
logAndBuffer("INFO", "脚本开始执行主要逻辑...");
// ... 您的脚本代码 ...
if (errorCondition) {
  logAndBuffer("ERROR", "发生了一个错误:", errorDetails);
}
logAndBuffer("DEBUG", "当前变量值为:", someVariable);
```

### 3.3. 脚本结束时写入日志表

在脚本的顶层 `try...catch...finally` 结构的 `finally` 块中调用 `writeAirScriptLogsToWpsTable`：

```javascript
// 示例 IIFE (Immediately Invoked Function Expression) 结构
(function MainExecutionWrapper() {
  SCRIPT_EXECUTION_START_TIME = new Date(); // 确保在开始时重置
  try {
    // ... 您的主脚本逻辑 ...
    logAndBuffer("INFO", "主逻辑执行完毕。");

  } catch (e) {
    logAndBuffer("ERROR", "脚本顶层执行发生致命错误: " + (e.message || JSON.stringify(e)));
    if (e.stack) logAndBuffer("ERROR", "顶层错误堆栈: " + e.stack);
  } finally {
    // 调用通用日志函数 - 非常简单！
    const loggingConfig = {
      logBuffer: GLOBAL_LOG_BUFFER,
      logTableName: LOG_TABLE_NAME,
      scriptStartTime: SCRIPT_EXECUTION_START_TIME
    };
    
    const loggingOutcome = writeAirScriptLogsToWpsTable(loggingConfig);

    if (!loggingOutcome.success) {
      console.error("!! 严重错误: 脚本完成时未能将脚本执行日志写入WPS表: " + loggingOutcome.error);
    }
  }
})();
```

## 4. 参数详解 (`writeAirScriptLogsToWpsTable` config)

- `logBuffer` (Array<string>): **必需**. 存储日志消息字符串的数组。
- `logTableName` (string): **必需**. 日志要写入的多维表的名称。
- `scriptStartTime` (Date): **必需**. 脚本开始执行的时间（`new Date()` 对象）。

## 5. 日志表结构

使用此规范创建的日志表将包含以下固定字段：

- **执行时间** (MultiLineText): 记录 `scriptStartTime` 的格式化字符串。
- **日志内容** (MultiLineText): 记录 `logBuffer` 合并后的所有日志条目。

## 6. 最佳实践与注意事项

- **日志表名统一管理**：考虑在项目或团队层面统一日志表的命名约定。
- **日志级别使用**：合理使用 INFO、ERROR、WARN、DEBUG 等日志级别。
- **错误处理**：检查返回结果的 `success` 和 `error` 属性，以处理日志写入失败的情况。
- **性能考量**：对于执行非常频繁的脚本，可以考虑调整日志级别或采样记录。
- **清理旧日志**：定期归档或清理旧的日志记录，以防日志表无限膨胀。

通过遵循此简化规范，您的WPS AirScript项目将拥有一个简单而有效的日志记录框架。 