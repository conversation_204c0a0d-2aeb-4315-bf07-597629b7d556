# WPS_AirScript_脚本参数使用指南

## 一、 功能简介

AirScript 脚本是 WPS 提供的一种自动化处理能力，它允许开发者通过编写脚本来实现对 WPS 应用内数据的操作、与外部服务交互等功能。本指南旨在提供 AirScript 脚本开发的基础知识和实践示例。

## 二、 参数说明

### 1. 脚本入参

脚本可以通过预设的 JSON 格式来接收外部传入的参数。在脚本中，可以通过 `context.argv` 对象来获取这些参数。

**示例：**

假设入参 JSON 如下：

```json
{
  "name": "张三",
  "age": 30
}
```

在 AirScript 脚本中可以通过以下方式获取：

```javascript
let name = context.argv.name; // 获取 name 参数
let age = context.argv.age;   // 获取 age 参数
```

### 2. 脚本返回值

脚本执行完毕后，可以通过 `return` 语句返回结果。返回的结果可以是任意合法的 JavaScript 数据类型，通常建议返回 JSON 对象，以便调用方能够清晰地解析和使用。

**示例：**

```javascript
// ... 脚本逻辑 ...
return {
  status: "success",
  data: {
    message: "操作成功完成"
  }
};
```

## 三、 场景示例

### 1. 场景1：新增记录时，执行脚本（使用脚本发送消息，并引用新增记录的结果）

当在 WPS 应用中新增一条记录时，可以触发执行一个 AirScript 脚本。该脚本可以获取新增记录的ID、名称等信息，并利用这些信息执行后续操作，例如发送企业微信消息。

**流程示意：**

1.  **触发条件：** 新增记录。
2.  **执行动作：** 执行 AirScript 脚本。
3.  **脚本逻辑：**
    *   获取新增记录的ID (`recordId`) 和名称 (`name`) 等字段。
    *   构造企业微信消息内容。
    *   通过 `http.post` 方法将消息发送到指定的 webhook 地址。
    *   处理发送结果，如果失败则抛出错误。

**脚本示例：**

```javascript
/**
 * 函数名称：sendNotificationOnNewRecord
 *
 * 概述: 当新增记录时，发送企业微信通知。
 * 详细描述: 获取新增记录的ID和名称，构造消息体，通过HTTP POST请求发送到企业微信的webhook。
 * 调用的函数: 无
 * 参数:
 *   context (object): 脚本执行上下文，包含入参。
 *     - context.argv.names (string): 记录名称。
 *     - context.argv.nums (string): 记录数量的字符串表示，可能包含单位。
 *     - context.argv.recordIds (array): 新增记录的ID数组。
 * 返回值:
 *   object: 包含执行状态和从webhook返回的数据。
 * 异常:
 *   Error: "发送企业微信机器人消息失败" - 当HTTP请求状态码非200时抛出。
 * 修改时间: 2025-05-23 16:50
 */
let names = context.argv.names;
let nums = context.argv.nums; // 示例中的 "数量" 字段，实际可能是其他字段
let recordIds = context.argv.recordIds;

let content = "您有新的记录：";
for (let i = 0; i < recordIds.length; i++) {
    let recordId = recordIds[i];
    let name = names[i]; // 假设 names 和 recordIds 是一一对应的
    // 注意：示例图片中的 nums 看起来是单个值，如果每个记录都有数量，需要调整获取方式
    // 这里假设 nums 是一个与记录相关的通用描述或数量
    content += `
ID: ${recordId}, 名称: ${name}, 数量: ${nums}`;
}

let resp = http.post({
    url: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxxxxx", // 请替换为您的真实webhook key
    json: {
        "msgtype": "text",
        "text": {
            "content": content
        }
    },
    timeout: 2000 // 设置超时时间
});

if (resp.status !== 200) {
    console.log("发送企业微信机器人消息失败, status: " + resp.status); // 打印日志
    throw new Error("发送企业微信机器人消息失败");
}

let respJson = resp.json();
if (respJson.errcode !== 0) {
    console.log("发送企业微信机器人消息失败, errcode: " + respJson.errcode + ", errmsg: " + respJson.errmsg); // 打印日志
    throw new Error("发送企业微信机器人消息失败");
}

return respJson;
```

### 2. 场景2：定时执行，执行脚本（获取外部数据）- 新增记录（引用脚本获取的数据）

可以设置定时任务，在指定时间执行 AirScript 脚本。脚本可以调用外部 API 获取数据，然后将获取到的数据新增到 WPS 应用的记录中。

**流程示意：**

1.  **触发条件：** 定时任务触发。
2.  **执行动作：** 执行 AirScript 脚本。
3.  **脚本逻辑：**
    *   通过 `http.get` 或 `http.post` 方法请求外部 API 获取数据。
    *   处理 API 返回的数据，解析出需要的信息。
    *   将解析后的数据作为脚本的返回值。
4.  **后续动作（WPS 流程配置）：** WPS 流程会接收脚本返回的数据，并根据配置将数据新增到指定的表中。

**脚本示例 (获取汇率数据)：**

```javascript
/**
 * 函数名称：fetchExchangeRate
 *
 * 概述: 从外部API获取汇率数据。
 * 详细描述: 通过HTTP GET请求访问指定的汇率API，并返回获取到的数据。
 * 调用的函数: 无
 * 参数:
 *   context (object): 脚本执行上下文。
 * 返回值:
 *   object: 包含从API获取的汇率数据，如果请求失败或返回数据格式不正确，则返回空数组或错误信息。
 * 异常:
 *   Error: "获取汇率数据失败" - 当HTTP请求状态码非200时抛出。
 * 修改时间: 2025-05-23 16:50
 */
let resp = http.get({
    url: "https://api.songzixian.com/api/exchange-rate?datasource=oppo" // 示例API地址，请按需替换
});

if (resp.status !== 200) {
    console.log("获取汇率数据失败, status: " + resp.status);
    throw new Error("获取汇率数据失败");
}

let respJson = resp.json();
if (respJson.code !== 200) { // 假设API成功时返回的code是200
    console.log("获取汇率数据API返回错误, code: " + respJson.code + ", message: " + respJson.message);
    // 根据实际API返回的错误结构进行处理，这里简单返回一个包含错误信息的对象
    return { error: "API_ERROR", message: respJson.message, data: { rates: [] } };
}

// 假设返回的数据在 respJson.data.rates 中
return respJson.data.rates || [];
```

## 四、常见问题

### 1. 问：什么是 dataframe 格式数据？

答：Dataframe 是一种表格型数据结构，类似于关系型数据库中的表或电子表格。它由行和列组成，每一列可以有不同的数据类型。在 AirScript 的上下文中，当脚本被用于【新增记录】、【修改记录】时，或者【查找内容】、【获取数据】、【获取抖店音视频数据】等操作返回数据时，其指定的输出为 dataframe 格式。

**Dataframe 结构示例 (JSON 表示)：**

```json
{
  "columns": [ // 列信息，包含列ID、列名、列类型
    {
      "id": "a",
      "name": "第一列",
      "type": "string" // 该列为文本类型
    },
    {
      "id": "b",
      "name": "第二列",
      "type": "string" // 该列为文本类型
    },
    {
      "id": "c",
      "name": "第三列",
      "type": "string" // 该列为文本类型
    }
  ],
  "cells": [ // 行数据，每个子数组为一行，子项为该行每列的数据
    ["第一行A", "第一行B", "第一行C"],
    ["第二行A", "第二行B", "第二行C"]
  ]
}
```

其中 `Columns` 字段定义了表格的列信息，包括：
*   `id`: 列的唯一标识符。
*   `name`: 列的显示名称。
*   `type`: 列的数据类型 (例如: `string`, `number`, `date` 等)。

`cells` 字段是一个二维数组，每一项代表一行数据，数组内的元素对应于 `columns` 中定义的每一列的值。

### 2. 问：当选择 dataframe 作为变量类型后，数据格式是怎样的？

答：若选择 dataframe 中的**某一列**，则变量类型为数组，每一项是单行记录该列的值。

**变量选择示例：**

选择 `名称` 列（假设其 `id` 为 `id`）。

**脚本中的入参示例：**

当 dataframe 的某一列（例如 `名称` 列，其 `id` 为 `name_column_id`）被选作脚本入参时，脚本中获取到的该参数会是一个数组，数组中的每个元素是该列在一行中的值。

```javascript
// 假设入参 context.argv.p1 对应了 dataframe 中的 "名称" 列
let nameList = context.argv.p1; // nameList 会是一个数组，如 ["张三", "李四", "王五"]
console.log(nameList);
```

### 3. 问：什么时候会符合 JSON 格式规范的内容？

答：当从以下4个角度确认输入的内容是否符合 JSON 格式规范：

1.  **结构体中需包含一个或多个键值对 (Key-Value)。**
2.  **Key 必须为英文双引号包裹的字符串。**
3.  **Value 的类型可以是字符串、数字、布尔值、数组、对象或 null。**
    *   字符串：必须用英文双引号包裹。
    *   数字：整数或浮点数，不需要引号。
    *   布尔值：`true` 或 `false`，不需要引号。
    *   数组：用方括号 `[]` 包裹，元素之间用逗号 `,` 分隔。
    *   对象：用花括号 `{}` 包裹，键值对之间用逗号 `,` 分隔。
4.  **对象以大括号 `{}` 开始和结束，不兼容在末尾添加英文双引号。若引用文本类型的变量，则需要在变量前后添加英文双引号。**

**JSON 格式示例：**

```json
{
  "text": "这是一个文本", // Key 和 String Value 都用双引号
  "arr": [1, "二", true], // 数组包含不同类型的元素
  "num": 123,
  "obj": {
    "body": {
      "text1": "嵌套对象的值"
    }
  }
}
```

**错误示例（末尾多了双引号）：**

```json
{
  "text": "错误示例"
}" // 错误：末尾不应有双引号
```

**引用文本变量的正确方式：**

假设有一个脚本变量 `myVariable` 的值为字符串 `"Hello World"`。

在构造 JSON 时，如果需要将这个变量的值作为 JSON 字符串的值，需要确保它被正确地包含在双引号内。

```javascript
// 脚本中的变量
let myVariable = "Hello World";

// 构造 JSON 对象
let jsonObj = {
  "message": myVariable // 正确，因为 myVariable 本身是字符串，在 JSON 序列化时会自动处理
};

// 如果要在字符串模板中构造 JSON 字符串，需要注意引号：
let jsonString = `{
  "message": "${myVariable}" // 如果 myVariable 包含特殊字符，可能需要进一步转义
}`;

// 如果是传递给 http.post 的 json 参数，直接使用 JavaScript 对象即可，WPS 会自动序列化
http.post({
  url: "...",
  json: {
    "message": myVariable
  }
});
```

## 五、总结

本指南介绍了 WPS AirScript 的基本功能、参数传递、常见场景示例以及一些常见问题。掌握这些内容将有助于开发者更高效地利用 AirScript 实现自动化流程和功能扩展。在实际开发中，请务必参考最新的官方文档和API说明。

---
*修改时间: 2025-05-23 16:50*

## 重要开发实践与避坑指南 (根据实际调试经验总结)

在WPS AirScript的开发过程中，遵循一些最佳实践并注意常见的陷阱，可以显著提高开发效率和脚本的稳定性。以下是基于实际调试经验总结的关键点：

1.  **严格的API参数类型与值校验：**
    *   **`SheetId` 必须为数字**：所有接受 `SheetId` 的WPS API都要求其为数字类型。从 `Context.argv` 或其他API获取后，务必使用 `Number()` 进行转换和校验。例如，`Number(params.sheetId)`。
    *   **字段类型精确匹配**：使用 `Application.Sheet.CreateSheet` 或 `Application.Field.CreateFields` 创建表或字段时，提供的字段类型 (`type`) 必须是WPS API明确支持的类型。例如，没有 `DateTime` 类型，对于日期时间字符串，应使用 `MultiLineText` 或拆分为 `Date` 和 `Time` 字段。请参考官方最新的有效字段类型列表。
    *   **`Context.argv` 参数结构**：从自动化流程传递到 `Context.argv` 的参数，即使逻辑上是单个值（如记录ID、表ID），也可能以数组形式（如 `{"数据表ID": [87]}`）出现。脚本中接收时，应检查其类型 (`Array.isArray()`)，如果是数组且预期为单个值，则取其第一个元素 (e.g., `sheetIdFromContext = Array.isArray(sheetIdFromContext) && sheetIdFromContext.length > 0 ? sheetIdFromContext[0] : sheetIdFromContext;`)。

2.  **精确的名称匹配：**
    *   查找或比较表名、字段名时，WPS API通常执行严格的、区分大小写的字符串匹配。一个微小的拼写错误（如 `MyTable` vs `MyTable ` 或我们遇到的 `ScriptExecutionLogs_WorkshopTim` vs `ScriptExecutionLogs_WorkshopTime`）都会导致匹配失败，从而可能引发非预期的行为（如重复创建表）。
    *   在代码中定义表名或字段名常量时，务必与WPS多维表中的实际名称或期望名称完全一致。使用 `String(nameFromApi).trim() === String(expectedName).trim()` 进行比较可以增加一些稳健性，但根本上要确保定义和使用的一致性。

3.  **JavaScript特性与环境兼容性：**
    *   **`async/await`慎用**：尽管AirScript支持ES6，但对 `async/await` 的支持可能不完整或在特定上下文中（如顶层或非async函数内）存在问题，可能导致"不支持await"等运行时错误。大多数 `Application.*` API调用本身是同步的。如非必要（例如，调用 `HTTP.fetch` 且需要处理其Promise特性），建议优先采用同步编码风格，以避免兼容性问题。
    *   **循环遍历API返回数组**：WPS官方文档建议，为确保稳定可靠地遍历从 `Application.*` API（如 `Application.Sheet.GetSheets()`）返回的数组状结果，应优先使用传统的 `for` 循环 ( `for (let i = 0; i < array.length; i++)`)，而非 `forEach` 等现代迭代方法，因后者在AirScript环境中的行为可能与预期不一致。

4.  **对象构造的稳健性 (API参数)：**
    *   WPS官方文档提示，在使用如 `Application.Field.CreateFields` 或 `Application.Record.CreateRecords` 时，若需要构造包含多个对象（如字段定义、记录数据）的数组作为参数，直接使用 `.map(item => ({...}))` 的方式有时可能导致对象属性"丢失"或API调用失败（例如，对象内的 `name` 属性可能未被正确传递）。
    *   **推荐做法**：当遇到此类问题时，应改用传统的 `for` 循环，在循环中显式创建每个对象 (`let field_object = {}; field_object.name = ...;`)并设置其属性，然后将这些对象 `push` 到一个数组中，再将该数组传递给API。

5.  **全面的日志与错误处理：**
    *   **大量使用 `console.log`**：在开发和调试阶段，对关键变量的值、类型 (`typeof`)、API调用的输入参数和输出结果进行日志记录至关重要。`JSON.stringify()` 可以帮助查看对象和数组的完整结构。
    *   **结构化日志**：对于复杂脚本，可以像我们调试中实现的那样，建立一个日志缓冲区 (`GLOBAL_LOG_BUFFER`)，并将格式化的日志条目（包含时间戳、级别、消息）存入其中。
    *   **日志持久化**：可以将日志缓冲区的内容写入专门的WPS多维日志表，便于追踪和问题排查。注意，写入日志表的操作本身不应再触发主日志缓冲机制（即日志写入函数内部应使用 `console.log` 等直接输出），以避免递归。
    *   **`try...catch` 包裹API调用**：所有对WPS API的调用以及其他可能抛出异常的操作（如HTTP请求）都应包裹在 `try...catch` 块中，以便捕获和记录错误详情（如 `e.message` 和 `e.stack`）。

6.  **自动化流程参数命名一致性：**
    *   当脚本依赖于自动化流程通过 `Context.argv` 传入参数时（例如 `数据表ID`，`记录ID`），务必确保自动化流程配置中传递给脚本的参数键名与脚本中读取 `Context.argv` 时使用的键名完全一致（包括大小写和语言）。例如，如果脚本使用 `Context.argv["数据表ID"]`，则自动化流程中对应的参数名也必须是 "数据表ID"。

遵循这些实践，可以帮助您更顺利地开发和维护WPS AirScript脚本，减少调试时间，并提高脚本的健壮性。

## 五、总结

本指南介绍了 WPS AirScript 的基本功能、参数传递、常见场景示例以及一些常见问题。掌握这些内容将有助于开发者更高效地利用 AirScript 实现自动化流程和功能扩展。在实际开发中，请务必参考最新的官方文档和API说明。 