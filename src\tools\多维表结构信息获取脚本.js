/**
 * 模块名称：多维表结构信息获取脚本
 * 模块描述：获取当前多维表中所有表、字段和字段格式等详细信息
 * 模块职责：遍历并收集多维表的完整结构信息，便于分析和管理
 * 修改时间: 2025-07-17 17:45
 */

// -------- 全局日志记录系统 --------
const GLOBAL_LOG_BUFFER = []; // 用于缓存所有日志条目
const LOG_TABLE_NAME = "脚本执行日志"; // 日志表名称
let SCRIPT_EXECUTION_START_TIME = new Date(); // 记录脚本开始执行的精确时间

//------------------
// 日志记录系统函数
//------------------

/**
 * 辅助函数：将Date对象格式化为 "YYYY/MM/DD HH:MM:SS" 字符串 (WPS DateTime 格式)
 * @param {Date} date - 要格式化的Date对象。
 * @returns {string} 格式化后的日期时间字符串。
 */
function formatDateTimeForWpsTable(date) {
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    date = new Date(); // 如果日期无效，则回退到当前时间
  }
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从0开始
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 辅助函数：将Date对象格式化为 "YYYY-MM-DD HH:MM:SS.mmm" 字符串 (包含毫秒，用于控制台日志)
 */
function formatDateTimeWithMs(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  const milliseconds = String(date.getMilliseconds()).padStart(3, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
}

/**
 * 统一日志记录函数
 * @param {string} level - 日志级别 (INFO, ERROR, WARN, DEBUG)
 * @param {...any} messages - 要记录的消息内容
 */
function logAndBuffer(level, ...messages) {
  const timestamp = formatDateTimeWithMs(new Date());
  const logEntry = `${timestamp} [${level.toUpperCase()}] ${messages.join(
    " "
  )}`;
  GLOBAL_LOG_BUFFER.push(logEntry);

  switch (level.toUpperCase()) {
    case "INFO":
      console.info(logEntry);
      break;
    case "ERROR":
      console.error(logEntry);
      break;
    case "WARN":
      console.warn(logEntry);
      break;
    case "DEBUG":
      console.log(logEntry);
      break;
    default:
      console.log(logEntry);
  }
}

/**
 * 函数名称: writeAirScriptLogsToWpsTable
 *
 * 概述: 将AirScript的执行日志写入指定的WPS多维数据表（简化版）。
 * 详细描述:
 *   1. 检查指定的日志表是否存在。
 *   2. 如果日志表不存在，则创建包含两个固定字段的新表。
 *   3. 如果日志表存在，则检查是否包含必需的两个字段。
 *   4. 如果有必需的字段缺失，则尝试在日志表中创建这些字段。
 *   5. 将传入的日志缓冲内容和脚本执行开始时间写入日志表的新记录中。
 *   注意: 此函数依赖WPS AirScript环境中的 `Application` 全局对象。
 *
 * @param {object} config - 日志写入配置对象。
 * @param {string[]} config.logBuffer - 包含日志条目（字符串）的数组。通常是脚本中维护的全局日志数组。
 * @param {string} config.logTableName - 要写入日志的目标多维表的名称。
 * @param {Date} config.scriptStartTime - 主脚本开始执行的时间 (Date对象)。
 *
 * @returns {object} result - 包含执行状态的对象。
 * @returns {boolean} result.success - 日志是否成功写入。
 * @returns {string|null} result.logRecordId - 如果成功，则为新创建的日志记录的ID；否则为null。
 * @returns {string|null} result.error - 如果失败，则为错误消息。
 * 修改时间: 2025-07-17 17:45
 */
function writeAirScriptLogsToWpsTable(config) {
  // 参数解构
  const { logBuffer, logTableName, scriptStartTime } = config;

  const result = { success: false, logRecordId: null, error: null };

  if (
    typeof Application === "undefined" ||
    typeof Application.Sheet === "undefined"
  ) {
    result.error = "Application 或 Application.Sheet 未定义。可能非WPS环境。";
    console.error("[writeAirScriptLogsToWpsTable] " + result.error);
    if (logBuffer && logBuffer.length > 0) {
      console.error(
        "[writeAirScriptLogsToWpsTable] 缓存的日志:\n" + logBuffer.join("\n")
      );
    }
    return result;
  }

  if (!logBuffer || logBuffer.length === 0) {
    result.success = true; // 认为操作成功，因为没有日志需要写
    return result;
  }

  const logContentForTable = logBuffer.join("\n");

  // 固定的字段定义
  const FIXED_LOG_FIELDS = [
    { name: "执行时间", type: "MultiLineText" },
    { name: "日志内容", type: "MultiLineText" },
  ];

  let logSheetId = null;
  try {
    const sheets = Application.Sheet.GetSheets();
    let existingLogSheet = null;
    if (Array.isArray(sheets)) {
      for (let i = 0; i < sheets.length; i++) {
        if (sheets[i] && String(sheets[i].name) === String(logTableName)) {
          existingLogSheet = sheets[i];
          break;
        }
      }
    }

    if (existingLogSheet) {
      logSheetId = Number(existingLogSheet.id);

      const existingFieldsResult = Application.Field.GetFields({
        SheetId: logSheetId,
      });
      const existingFieldNames = [];
      if (Array.isArray(existingFieldsResult)) {
        for (let i = 0; i < existingFieldsResult.length; i++) {
          if (existingFieldsResult[i] && existingFieldsResult[i].name != null) {
            existingFieldNames.push(String(existingFieldsResult[i].name));
          }
        }
      }

      const fieldsToAdd = [];
      for (let i = 0; i < FIXED_LOG_FIELDS.length; i++) {
        const requiredField = FIXED_LOG_FIELDS[i];
        if (
          !existingFieldNames.some(
            (name) => String(name) === String(requiredField.name)
          )
        ) {
          fieldsToAdd.push({
            name: String(requiredField.name),
            type: String(requiredField.type),
          });
        }
      }

      if (fieldsToAdd.length > 0) {
        try {
          const createFieldsResult = Application.Field.CreateFields({
            SheetId: logSheetId,
            Fields: fieldsToAdd,
          });
          if (
            !createFieldsResult ||
            !Array.isArray(createFieldsResult) ||
            createFieldsResult.length !== fieldsToAdd.length ||
            createFieldsResult.some((f) => !f || typeof f.id === "undefined")
          ) {
            console.error(
              `[错误][writeAirScriptLogsToWpsTable] 未能向 '${logTableName}' 添加部分/全部缺失字段. API响应: ${JSON.stringify(
                createFieldsResult
              )}`
            );
          }
        } catch (fieldCreationError) {
          console.error(
            `[错误][writeAirScriptLogsToWpsTable] 在为 '${logTableName}' 执行 Application.Field.CreateFields 时发生错误: ${
              fieldCreationError.message || JSON.stringify(fieldCreationError)
            }`
          );
        }
      }
    } else {
      try {
        const newSheet = Application.Sheet.CreateSheet({
          Name: String(logTableName),
          Views: [{ name: "所有日志", type: "Grid" }],
          Fields: FIXED_LOG_FIELDS,
        });
        if (newSheet && typeof newSheet.id !== "undefined") {
          logSheetId = Number(newSheet.id);
        } else {
          result.error = `创建日志表 '${logTableName}' 失败. API响应: ${JSON.stringify(
            newSheet
          )}`;
          console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
          return result;
        }
      } catch (sheetCreationError) {
        result.error = `在为 '${logTableName}' 执行 Application.Sheet.CreateSheet 时发生错误: ${
          sheetCreationError.message || JSON.stringify(sheetCreationError)
        }`;
        console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
        return result;
      }
    }

    if (logSheetId !== null) {
      const executionTimeFormatted = formatDateTimeForWpsTable(scriptStartTime);
      const recordDataFields = {
        执行时间: executionTimeFormatted,
        日志内容: logContentForTable,
      };

      try {
        const createRecordParams = {
          SheetId: logSheetId,
          Records: [{ fields: recordDataFields }],
        };
        const createResult =
          Application.Record.CreateRecords(createRecordParams);

        if (
          createResult &&
          Array.isArray(createResult) &&
          createResult.length > 0 &&
          typeof createResult[0].id !== "undefined"
        ) {
          result.success = true;
          result.logRecordId = createResult[0].id;
        } else {
          result.error = `未能将日志写入表 '${logTableName}'. API响应: ${JSON.stringify(
            createResult
          )}`;
          console.error(
            "[错误][writeAirScriptLogsToWpsTable] " +
              result.error +
              " 数据: " +
              JSON.stringify(recordDataFields)
          );
        }
      } catch (recordCreationError) {
        result.error = `在为 '${logTableName}' 执行 Application.Record.CreateRecords 时发生错误: ${
          recordCreationError.message || JSON.stringify(recordCreationError)
        }`;
        console.error(
          "[错误][writeAirScriptLogsToWpsTable] " +
            result.error +
            " 数据: " +
            JSON.stringify(recordDataFields)
        );
      }
    } else {
      result.error = "日志表的logSheetId为空，无法写入日志。";
      console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
    }
  } catch (e) {
    result.error = `在 writeAirScriptLogsToWpsTable 中发生意外错误: ${
      e.message || JSON.stringify(e)
    }`;
    console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
    if (e.stack)
      console.error(
        "[错误][writeAirScriptLogsToWpsTable] 错误堆栈: " + e.stack
      );
  }
  return result;
}

//------------------
// 时区处理函数
//------------------

/**
 * 获取中国时区（UTC+8）的当前时间
 *
 * 概述: 解决AirScript运行环境时区问题，确保获取准确的中国时区时间
 * 详细描述: 通过UTC时间戳计算，避免直接使用new Date()可能导致的时区偏差
 * 调用的函数: 无
 * 返回值: Date - 中国时区的当前时间对象
 * 修改时间: 2025-07-17 17:45
 */
function getChinaTime() {
  const now = new Date();
  // 获取UTC时间戳
  const utcTime = now.getTime() + now.getTimezoneOffset() * 60000;
  // 添加8小时（中国时区UTC+8）
  const chinaTime = new Date(utcTime + 8 * 3600000);
  return chinaTime;
}

/**
 * 获取中国时区（UTC+8）的当前时间字符串
 *
 * 概述: 格式化中国时区时间为指定格式的字符串
 * 详细描述: 支持多种时间格式，默认为完整的日期时间格式
 * 调用的函数: getChinaTime()
 * 参数: format (string) - 时间格式，默认为 'YYYY-MM-DD HH:mm:ss'
 * 返回值: string - 格式化的中国时区时间字符串
 * 修改时间: 2025-07-17 17:45
 */
function getChinaTimeStr(format = "YYYY-MM-DD HH:mm:ss") {
  const chinaTime = getChinaTime();

  const year = chinaTime.getFullYear();
  const month = String(chinaTime.getMonth() + 1).padStart(2, "0");
  const day = String(chinaTime.getDate()).padStart(2, "0");
  const hours = String(chinaTime.getHours()).padStart(2, "0");
  const minutes = String(chinaTime.getMinutes()).padStart(2, "0");
  const seconds = String(chinaTime.getSeconds()).padStart(2, "0");

  if (format === "YYYY/MM/DD") {
    return `${year}/${month}/${day}`;
  } else if (format === "HH:mm:ss") {
    return `${hours}:${minutes}:${seconds}`;
  } else {
    // 默认格式 'YYYY-MM-DD HH:mm:ss'
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }
}

//------------------
// 字段类型说明映射
//------------------

/**
 * 获取字段类型的中文说明
 *
 * 概述: 将WPS多维表字段类型转换为易读的中文说明
 * 详细描述: 提供字段类型的中文名称和详细说明，便于理解字段用途
 * 调用的函数: 无
 * 参数: fieldType (string) - 字段类型英文标识
 * 返回值: object - 包含中文名称和说明的对象
 * 修改时间: 2025-07-17 17:45
 */
function getFieldTypeDescription(fieldType) {
  const typeMap = {
    MultiLineText: { name: "多行文本", description: "支持多行文本输入的字段" },
    Date: { name: "日期", description: "日期字段，格式为YYYY/MM/DD" },
    Time: { name: "时间", description: "时间字段，格式为HH:mm:ss" },
    Number: { name: "数值", description: "数字字段，支持小数" },
    Currency: { name: "货币", description: "货币金额字段" },
    Percentage: { name: "百分比", description: "百分比数值字段" },
    ID: { name: "身份证", description: "身份证号码字段" },
    Phone: { name: "电话", description: "电话号码字段" },
    Email: { name: "电子邮箱", description: "邮箱地址字段" },
    Url: { name: "超链接", description: "网址链接字段" },
    Checkbox: { name: "复选框", description: "布尔值字段，true/false" },
    SingleSelect: { name: "单选项", description: "单选下拉列表字段" },
    MultipleSelect: { name: "多选项", description: "多选下拉列表字段" },
    Rating: { name: "等级", description: "评级字段，支持1-5星等级" },
    Complete: { name: "进度条", description: "进度百分比字段，0-1之间的小数" },
    Contact: { name: "联系人", description: "联系人信息字段" },
    Attachment: { name: "附件", description: "文件附件字段" },
    Link: { name: "关联", description: "关联其他表记录的字段" },
    Note: { name: "富文本", description: "富文本文档字段，自动同步" },
    AutoNumber: { name: "编号", description: "自动递增编号字段" },
    CreatedBy: { name: "创建者", description: "记录创建者信息，自动字段" },
    CreatedTime: { name: "创建时间", description: "记录创建时间，自动字段" },
    Formula: { name: "公式", description: "公式计算字段，自动计算" },
    Lookup: { name: "引用", description: "引用其他字段的值，自动字段" },
  };

  return typeMap[fieldType] || { name: fieldType, description: "未知字段类型" };
}

/**
 * 获取视图类型的中文说明
 *
 * 概述: 将WPS多维表视图类型转换为易读的中文说明
 * 详细描述: 提供视图类型的中文名称和用途说明
 * 调用的函数: 无
 * 参数: viewType (string) - 视图类型英文标识
 * 返回值: object - 包含中文名称和说明的对象
 * 修改时间: 2025-07-17 17:45
 */
function getViewTypeDescription(viewType) {
  const typeMap = {
    Grid: { name: "表视图", description: "传统的表格形式展示数据" },
    Kanban: { name: "看板视图", description: "看板形式展示，适合任务管理" },
    Gallery: { name: "画册视图", description: "画册形式展示，适合图片内容" },
    Form: { name: "表单视图", description: "表单形式，适合数据录入" },
    Gantt: { name: "甘特视图", description: "甘特图形式，适合项目进度管理" },
  };

  return typeMap[viewType] || { name: viewType, description: "未知视图类型" };
}

//------------------
// 核心数据获取函数
//------------------

/**
 * 获取多维表中所有表的详细信息
 *
 * 概述: 获取当前多维表中所有表、视图、字段的完整结构信息
 * 详细描述: 遍历所有表，获取每个表的基本信息、视图列表、字段列表及详细属性
 * 调用的函数:
 *   - Application.Sheet.GetSheets() - 获取所有表信息
 *   - getFieldTypeDescription() - 获取字段类型说明
 *   - getViewTypeDescription() - 获取视图类型说明
 *   - getChinaTimeStr() - 获取中国时区时间
 * 返回值: object - 包含所有表信息的结构化对象
 * 异常: Error - API调用失败时抛出异常
 * 修改时间: 2025-07-17 17:45
 */
function getAllTableStructure() {
  try {
    logAndBuffer("INFO", "开始获取多维表结构信息...");

    // 获取所有表信息
    const sheets = Application.Sheet.GetSheets();
    logAndBuffer("INFO", `成功获取到 ${sheets.length} 个表`);

    const tableStructure = {
      summary: {
        totalTables: sheets.length,
        scanTime: getChinaTimeStr(),
        description: "多维表结构信息汇总",
      },
      tables: [],
    };

    // 遍历每个表，使用传统for循环确保稳定性
    for (let i = 0; i < sheets.length; i++) {
      const sheet = sheets[i];

      // 检查表对象有效性
      if (
        !sheet ||
        typeof sheet.id === "undefined" ||
        typeof sheet.name === "undefined"
      ) {
        logAndBuffer("WARN", `跳过无效的表对象，索引: ${i}`);
        continue;
      }

      logAndBuffer(
        "DEBUG",
        `正在处理表: ${String(sheet.name)} (ID: ${String(sheet.id)})`
      );

      const tableInfo = {
        basic: {
          id: sheet.id,
          name: String(sheet.name),
          recordsCount: sheet.recordsCount || 0,
          primaryFieldId: sheet.primaryFieldId || "",
          description: "表的基本信息",
        },
        views: [],
        fields: [],
      };

      // 处理视图信息
      if (sheet.views && Array.isArray(sheet.views)) {
        logAndBuffer(
          "DEBUG",
          `表 ${String(sheet.name)} 包含 ${sheet.views.length} 个视图`
        );

        for (let j = 0; j < sheet.views.length; j++) {
          const view = sheet.views[j];
          if (view && typeof view.id !== "undefined") {
            const viewTypeDesc = getViewTypeDescription(view.type);

            tableInfo.views.push({
              id: String(view.id),
              name: String(view.name || ""),
              type: view.type || "",
              typeName: viewTypeDesc.name,
              typeDescription: viewTypeDesc.description,
              recordsCount: view.recordsCount || 0,
            });
          }
        }
      }

      // 获取详细字段信息 - 单独调用API获取完整配置
      try {
        logAndBuffer(
          "DEBUG",
          `开始获取表 ${String(sheet.name)} 的详细字段信息...`
        );
        const detailedFields = Application.Field.GetFields({
          SheetId: Number(sheet.id),
        });

        if (detailedFields && Array.isArray(detailedFields)) {
          logAndBuffer(
            "DEBUG",
            `表 ${String(sheet.name)} 获取到 ${
              detailedFields.length
            } 个详细字段`
          );

          for (let k = 0; k < detailedFields.length; k++) {
            const field = detailedFields[k];
            if (field && typeof field.id !== "undefined") {
              const fieldTypeDesc = getFieldTypeDescription(field.type);

              const fieldInfo = {
                id: String(field.id),
                name: String(field.name || ""),
                type: field.type || "",
                typeName: fieldTypeDesc.name,
                typeDescription: fieldTypeDesc.description,
                isPrimary: field.id === sheet.primaryFieldId,
                // 添加创建字段时需要的原始配置
                creationConfig: {
                  name: String(field.name || ""),
                  type: field.type || "",
                },
              };

              logAndBuffer(
                "DEBUG",
                `处理字段: ${fieldInfo.name} (${fieldInfo.type})`
              );

              // 根据字段类型添加详细配置信息
              if (
                field.type === "SingleSelect" ||
                field.type === "MultipleSelect"
              ) {
                if (field.items && Array.isArray(field.items)) {
                  fieldInfo.options = [];
                  fieldInfo.creationConfig.items = [];

                  for (let l = 0; l < field.items.length; l++) {
                    const item = field.items[l];
                    if (item && typeof item.value !== "undefined") {
                      const optionInfo = {
                        id: String(item.id || ""),
                        value: String(item.value),
                        color: item.color || 0,
                      };
                      fieldInfo.options.push(optionInfo);
                      // 创建表时需要的格式
                      fieldInfo.creationConfig.items.push({
                        value: String(item.value),
                      });
                    }
                  }
                  logAndBuffer(
                    "DEBUG",
                    `${fieldInfo.type}字段 ${fieldInfo.name} 有 ${fieldInfo.options.length} 个选项`
                  );
                }
              }

              if (field.type === "Rating") {
                if (typeof field.max !== "undefined") {
                  fieldInfo.maxRating = field.max;
                  fieldInfo.creationConfig.max = field.max;
                }
              }

              if (field.type === "Contact") {
                // 联系人字段的详细配置
                fieldInfo.supportMulti = field.supportMulti || false;
                fieldInfo.noticeNewContact = field.noticeNewContact || false;
                fieldInfo.creationConfig.multipleContacts =
                  field.supportMulti || false;
                fieldInfo.creationConfig.noticeNewContact =
                  field.noticeNewContact || false;
              }

              if (field.type === "Url") {
                if (field.displayText) {
                  fieldInfo.displayText = String(field.displayText);
                  fieldInfo.creationConfig.displayText = String(
                    field.displayText
                  );
                }
              }

              if (field.type === "Link") {
                // 关联字段的详细配置
                fieldInfo.linkSheet = field.linkSheet || null;
                fieldInfo.multipleLinks = field.multipleLinks || false;
                if (field.linkSheet) {
                  fieldInfo.creationConfig.linkSheet = field.linkSheet;
                  fieldInfo.creationConfig.multipleLinks =
                    field.multipleLinks || false;
                }
              }

              // 处理其他可能的字段属性
              if (field.type === "Currency") {
                if (field.currencySymbol) {
                  fieldInfo.currencySymbol = field.currencySymbol;
                  fieldInfo.creationConfig.currencySymbol =
                    field.currencySymbol;
                }
              }

              if (field.type === "Number") {
                if (typeof field.precision !== "undefined") {
                  fieldInfo.precision = field.precision;
                  fieldInfo.creationConfig.precision = field.precision;
                }
              }

              // 添加其他可能有用的属性
              if (field.description) {
                fieldInfo.description = String(field.description);
              }

              tableInfo.fields.push(fieldInfo);
            }
          }
        } else {
          logAndBuffer(
            "WARN",
            `无法获取表 ${String(sheet.name)} 的详细字段信息`
          );
        }
      } catch (fieldError) {
        logAndBuffer(
          "ERROR",
          `获取表 ${String(sheet.name)} 字段信息时出错: ${fieldError.message}`
        );
        if (fieldError.stack) {
          logAndBuffer("ERROR", `字段获取错误堆栈: ${fieldError.stack}`);
        }
      }

      tableStructure.tables.push(tableInfo);
      logAndBuffer("DEBUG", `完成表 ${String(sheet.name)} 的信息收集`);
    }

    logAndBuffer("INFO", "多维表结构信息获取完成！");
    return tableStructure;
  } catch (error) {
    logAndBuffer("ERROR", `获取表结构信息时发生错误: ${error.message}`);
    if (error.stack) {
      logAndBuffer("ERROR", `错误堆栈: ${error.stack}`);
    }
    throw error;
  }
}

/**
 * 格式化输出表结构信息
 *
 * 概述: 将表结构信息以易读的格式输出到控制台
 * 详细描述: 结构化展示所有表、视图、字段的详细信息，便于查看和分析
 * 调用的函数:
 *   - getAllTableStructure() - 获取表结构信息
 *   - getChinaTimeStr() - 获取中国时区时间
 * 返回值: object - 返回获取到的完整表结构信息
 * 异常: Error - 数据获取或处理失败时抛出异常
 * 修改时间: 2025-07-17 17:45
 */
function displayTableStructure() {
  try {
    const structure = getAllTableStructure();

    console.log("\n" + "=".repeat(60));
    console.log("多维表结构信息汇总报告");
    console.log("=".repeat(60));
    console.log(`扫描时间: ${structure.summary.scanTime}`);
    console.log(`表的总数: ${structure.summary.totalTables}`);
    console.log("=".repeat(60));

    // 遍历并显示每个表的详细信息
    for (let i = 0; i < structure.tables.length; i++) {
      const table = structure.tables[i];

      console.log(
        `\n【表 ${i + 1}】${table.basic.name} (ID: ${table.basic.id})`
      );
      console.log(`  记录数量: ${table.basic.recordsCount}`);
      console.log(`  主字段ID: ${table.basic.primaryFieldId}`);

      // 显示视图信息
      console.log(`\n  视图列表 (${table.views.length} 个):`);
      if (table.views.length === 0) {
        console.log("    暂无视图");
      } else {
        for (let j = 0; j < table.views.length; j++) {
          const view = table.views[j];
          console.log(
            `    ${j + 1}. ${view.name} (${view.typeName}) - ${
              view.recordsCount
            } 条记录`
          );
          console.log(`       ID: ${view.id}, 类型: ${view.type}`);
          console.log(`       说明: ${view.typeDescription}`);
        }
      }

      // 显示字段信息
      console.log(`\n  字段列表 (${table.fields.length} 个):`);
      if (table.fields.length === 0) {
        console.log("    暂无字段");
      } else {
        for (let k = 0; k < table.fields.length; k++) {
          const field = table.fields[k];
          const primaryMark = field.isPrimary ? " [主字段]" : "";
          console.log(
            `    ${k + 1}. ${field.name} (${field.typeName})${primaryMark}`
          );
          console.log(`       ID: ${field.id}, 类型: ${field.type}`);
          console.log(`       说明: ${field.typeDescription}`);

          // 显示字段特殊属性和创建配置
          if (field.options && field.options.length > 0) {
            console.log(
              `       选项 (${field.options.length}个): ${field.options
                .map((opt) => opt.value)
                .join(", ")}`
            );
          }
          if (typeof field.maxRating !== "undefined") {
            console.log(`       最大等级: ${field.maxRating}`);
          }
          if (typeof field.supportMulti !== "undefined") {
            console.log(
              `       支持多联系人: ${field.supportMulti ? "是" : "否"}`
            );
            console.log(
              `       通知新联系人: ${field.noticeNewContact ? "是" : "否"}`
            );
          }
          if (field.displayText) {
            console.log(`       显示文本: ${field.displayText}`);
          }
          if (field.linkSheet) {
            console.log(
              `       关联表ID: ${field.linkSheet}, 多条关联: ${
                field.multipleLinks ? "是" : "否"
              }`
            );
          }
          if (field.currencySymbol) {
            console.log(`       货币符号: ${field.currencySymbol}`);
          }
          if (typeof field.precision !== "undefined") {
            console.log(`       数字精度: ${field.precision}`);
          }
          if (field.description) {
            console.log(`       字段描述: ${field.description}`);
          }

          // 显示创建配置（用于复制表结构）
          if (field.creationConfig) {
            console.log(
              `       创建配置: ${JSON.stringify(
                field.creationConfig,
                null,
                2
              ).replace(/\n/g, "\n           ")}`
            );
          }
        }
      }

      console.log("\n" + "-".repeat(50));
    }

    console.log("\n" + "=".repeat(60));
    console.log("报告生成完成！");
    console.log("=".repeat(60));

    return structure;
  } catch (error) {
    logAndBuffer("ERROR", `显示表结构信息时发生错误: ${error.message}`);
    if (error.stack) {
      logAndBuffer("ERROR", `错误堆栈: ${error.stack}`);
    }
    throw error;
  }
}

//------------------
// 主执行函数
//------------------

// 使用IIFE (Immediately Invoked Function Expression) 结构，按照日志记录规范
(function MainExecutionWrapper() {
  SCRIPT_EXECUTION_START_TIME = new Date(); // 确保在开始时重置

  logAndBuffer("INFO", "多维表结构信息获取脚本开始执行");
  logAndBuffer(
    "INFO",
    "脚本功能: 获取当前多维表中所有表、字段和字段格式等信息"
  );

  try {
    // 执行主要功能
    const structure = displayTableStructure();

    // 输出JSON格式的完整数据（可选，便于程序化处理）
    console.log("\n" + "=".repeat(60));
    console.log("JSON格式的完整数据:");
    console.log("=".repeat(60));
    console.log(JSON.stringify(structure, null, 2));

    logAndBuffer("INFO", "脚本执行成功完成");
  } catch (error) {
    logAndBuffer(
      "ERROR",
      `脚本顶层执行发生致命错误: ${error.message || JSON.stringify(error)}`
    );
    if (error.stack) {
      logAndBuffer("ERROR", `顶层错误堆栈: ${error.stack}`);
    }
  } finally {
    // 调用通用日志函数 - 按照规范写入日志表
    const loggingConfig = {
      logBuffer: GLOBAL_LOG_BUFFER,
      logTableName: LOG_TABLE_NAME,
      scriptStartTime: SCRIPT_EXECUTION_START_TIME,
    };

    const loggingOutcome = writeAirScriptLogsToWpsTable(loggingConfig);

    if (!loggingOutcome.success) {
      console.error(
        "!! 严重错误: 脚本完成时未能将脚本执行日志写入WPS表: " +
          loggingOutcome.error
      );
    } else {
      console.log(
        "✓ 脚本执行日志已成功写入表: " +
          LOG_TABLE_NAME +
          " (记录ID: " +
          loggingOutcome.logRecordId +
          ")"
      );
    }
  }
})();
