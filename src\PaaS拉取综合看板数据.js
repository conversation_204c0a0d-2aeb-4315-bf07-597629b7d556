/**
 * 脚本名称：从PaaS拉取综合看板数据
 *
 * 功能描述：从PaaS系统获取实时统计数据，更新到WPS的"综合看板"表
 * 数据流向：PaaS多表联合查询(实时统计) → WPS综合看板表
 * 使用接口：/zijiezhen/test_sql (查询数据)
 * 时间范围：仅处理当前月份的数据
 * 执行频率：建议每小时执行一次
 *
 * 修改时间: 2025-07-18 18:30
 */

// ===========================================
// 配置区
// ===========================================

const CONFIG = {
  // PaaS API配置
  PAAS_API_URL: "https://4689cn93cl12.vicp.fun/api/zijiezhen/test_sql",
  PAAS_API_KEY: "5a2f4fda3ab24bd0b10067ac701fecfd",

  // PaaS表名配置
  PAAS_BRAND_ARCHIVE: "tb___f_e624233ca743e567",
  PAAS_BRAND_TARGET: "tb___f_e624233ca743e567_197f862cf351062f",

  // WPS表名配置
  WPS_TARGET_TABLE: "综合看板",
  WPS_LOG_TABLE: "脚本执行日志",
};

// 全局变量
let logBuffer = [];
const scriptStartTime = new Date();

// ===========================================
// 辅助函数
// ===========================================

/**
 * 获取当前月份
 *
 * 概述: 获取当前月份的YYYY-MM格式字符串
 * 调用的函数: JavaScript内置Date对象方法
 * 返回值: string - 当前月份字符串
 * 修改时间: 2025-07-18 18:30
 */
function getCurrentMonth() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  return `${year}-${month}`;
}

/**
 * 格式化日期时间
 *
 * 概述: 将Date对象格式化为可读字符串
 * 调用的函数: JavaScript内置Date对象方法
 * 参数: date (Date) - 日期对象
 * 返回值: string - 格式化的日期时间字符串
 * 修改时间: 2025-07-18 18:30
 */
function formatDateTime(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 辅助函数：将Date对象格式化为 "YYYY/MM/DD HH:MM:SS" 字符串 (WPS DateTime 格式)
 *
 * 概述: WPS表格专用的日期时间格式化函数
 * 调用的函数: JavaScript内置Date对象方法
 * 参数: date (Date) - 要格式化的Date对象
 * 返回值: string - 格式化后的日期时间字符串
 * 修改时间: 2025-07-18 18:30
 */
function formatDateTimeForWpsTable(date) {
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    date = new Date(); // 如果日期无效，则回退到当前时间
  }
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从0开始
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * HTTP请求封装
 *
 * 概述: 向PaaS系统发送test_sql查询请求
 * 调用的函数: WPS AirScript内置fetch函数
 * 参数: sql (string) - SQL查询语句
 * 返回值: object - 包含成功标志和数据的结果对象
 * 修改时间: 2025-07-18 18:30
 */
function queryPaaSData(sql) {
  const requestData = {
    sql: sql,
  };

  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-API-Key": CONFIG.PAAS_API_KEY,
    },
    body: JSON.stringify(requestData),
  };

  try {
    logBuffer.push(`[DEBUG] 向PaaS发送查询: ${sql.substring(0, 100)}...`);

    const response = fetch(CONFIG.PAAS_API_URL, requestOptions);

    if (response.status === 200) {
      const result = JSON.parse(response.body);
      return {
        success: true,
        data: result.data || [],
      };
    } else {
      return {
        success: false,
        error: `HTTP ${response.status}: ${response.body}`,
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `请求异常: ${error.message || error}`,
    };
  }
}

/**
 * 标准日志记录函数：写入AirScript执行日志到WPS表格
 *
 * 概述: 将AirScript的执行日志写入指定的WPS多维数据表（标准版）
 * 详细描述:
 *   1. 检查指定的日志表是否存在
 *   2. 如果日志表不存在，则创建包含两个固定字段的新表
 *   3. 如果日志表存在，则检查是否包含必需的两个字段
 *   4. 如果有必需的字段缺失，则尝试在日志表中创建这些字段
 *   5. 将传入的日志缓冲内容和脚本执行开始时间写入日志表的新记录中
 * 调用的函数:
 *   - Application.Sheet.GetSheets
 *   - Application.Sheet.CreateSheet
 *   - Application.Field.GetFields
 *   - Application.Field.CreateFields
 *   - Application.Record.CreateRecords
 *   - formatDateTimeForWpsTable
 * 参数: config (object) - 日志写入配置对象
 * 返回值: object - 包含执行状态的对象
 * 修改时间: 2025-07-18 18:30
 */
function writeAirScriptLogsToWpsTable(config) {
  // 参数解构
  const { logBuffer, logTableName, scriptStartTime } = config;

  const result = { success: false, logRecordId: null, error: null };

  if (
    typeof Application === "undefined" ||
    typeof Application.Sheet === "undefined"
  ) {
    result.error = "Application 或 Application.Sheet 未定义。可能非WPS环境。";
    console.error("[writeAirScriptLogsToWpsTable] " + result.error);
    if (logBuffer && logBuffer.length > 0) {
      console.error(
        "[writeAirScriptLogsToWpsTable] 缓存的日志:\\n" + logBuffer.join("\\n")
      );
    }
    return result;
  }

  if (!logBuffer || logBuffer.length === 0) {
    result.success = true; // 认为操作成功，因为没有日志需要写
    return result;
  }

  const logContentForTable = logBuffer.join("\\n");

  // 固定的字段定义
  const FIXED_LOG_FIELDS = [
    { name: "执行时间", type: "MultiLineText" },
    { name: "日志内容", type: "MultiLineText" },
  ];

  let logSheetId = null;
  try {
    const sheets = Application.Sheet.GetSheets();
    let existingLogSheet = null;
    if (Array.isArray(sheets)) {
      for (let i = 0; i < sheets.length; i++) {
        if (sheets[i] && String(sheets[i].name) === String(logTableName)) {
          existingLogSheet = sheets[i];
          break;
        }
      }
    }

    if (existingLogSheet) {
      logSheetId = Number(existingLogSheet.id);

      const existingFieldsResult = Application.Field.GetFields({
        SheetId: logSheetId,
      });
      const existingFieldNames = [];
      if (Array.isArray(existingFieldsResult)) {
        for (let i = 0; i < existingFieldsResult.length; i++) {
          if (existingFieldsResult[i] && existingFieldsResult[i].name != null) {
            existingFieldNames.push(String(existingFieldsResult[i].name));
          }
        }
      }

      const fieldsToAdd = [];
      for (let i = 0; i < FIXED_LOG_FIELDS.length; i++) {
        const requiredField = FIXED_LOG_FIELDS[i];
        if (
          !existingFieldNames.some(
            (name) => String(name) === String(requiredField.name)
          )
        ) {
          fieldsToAdd.push({
            name: String(requiredField.name),
            type: String(requiredField.type),
          });
        }
      }

      if (fieldsToAdd.length > 0) {
        try {
          const createFieldsResult = Application.Field.CreateFields({
            SheetId: logSheetId,
            Fields: fieldsToAdd,
          });
          if (
            !createFieldsResult ||
            !Array.isArray(createFieldsResult) ||
            createFieldsResult.length !== fieldsToAdd.length ||
            createFieldsResult.some((f) => !f || typeof f.id === "undefined")
          ) {
            console.error(
              `[错误][writeAirScriptLogsToWpsTable] 未能向 '${logTableName}' 添加部分/全部缺失字段. API响应: ${JSON.stringify(
                createFieldsResult
              )}`
            );
          }
        } catch (fieldCreationError) {
          console.error(
            `[错误][writeAirScriptLogsToWpsTable] 在为 '${logTableName}' 执行 Application.Field.CreateFields 时发生错误: ${
              fieldCreationError.message || JSON.stringify(fieldCreationError)
            }`
          );
        }
      }
    } else {
      try {
        const newSheet = Application.Sheet.CreateSheet({
          Name: String(logTableName),
          Views: [{ name: "所有日志", type: "Grid" }],
          Fields: FIXED_LOG_FIELDS,
        });
        if (newSheet && typeof newSheet.id !== "undefined") {
          logSheetId = Number(newSheet.id);
        } else {
          result.error = `创建日志表 '${logTableName}' 失败. API响应: ${JSON.stringify(
            newSheet
          )}`;
          console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
          return result;
        }
      } catch (sheetCreationError) {
        result.error = `在为 '${logTableName}' 执行 Application.Sheet.CreateSheet 时发生错误: ${
          sheetCreationError.message || JSON.stringify(sheetCreationError)
        }`;
        console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
        return result;
      }
    }

    if (logSheetId !== null) {
      const executionTimeFormatted = formatDateTimeForWpsTable(scriptStartTime);
      const recordDataFields = {
        执行时间: executionTimeFormatted,
        日志内容: logContentForTable,
      };

      try {
        const createRecordParams = {
          SheetId: logSheetId,
          Records: [{ fields: recordDataFields }],
        };
        const createResult =
          Application.Record.CreateRecords(createRecordParams);

        if (
          createResult &&
          Array.isArray(createResult) &&
          createResult.length > 0 &&
          typeof createResult[0].id !== "undefined"
        ) {
          result.success = true;
          result.logRecordId = createResult[0].id;
        } else {
          result.error = `未能将日志写入表 '${logTableName}'. API响应: ${JSON.stringify(
            createResult
          )}`;
          console.error(
            "[错误][writeAirScriptLogsToWpsTable] " +
              result.error +
              " 数据: " +
              JSON.stringify(recordDataFields)
          );
        }
      } catch (recordCreationError) {
        result.error = `在为 '${logTableName}' 执行 Application.Record.CreateRecords 时发生错误: ${
          recordCreationError.message || JSON.stringify(recordCreationError)
        }`;
        console.error(
          "[错误][writeAirScriptLogsToWpsTable] " +
            result.error +
            " 数据: " +
            JSON.stringify(recordDataFields)
        );
      }
    } else {
      result.error = "日志表的logSheetId为空，无法写入日志。";
      console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
    }
  } catch (e) {
    result.error = `在 writeAirScriptLogsToWpsTable 中发生意外错误: ${
      e.message || JSON.stringify(e)
    }`;
    console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
    if (e.stack)
      console.error(
        "[错误][writeAirScriptLogsToWpsTable] 错误堆栈: " + e.stack
      );
  }
  return result;
}

/**
 * 计算周进度状态
 *
 * 概述: 根据本周目标和已完成数计算周进度状态
 * 调用的函数: 无
 * 参数:
 *   weeklyTarget (number) - 本周目标
 *   completed (number) - 已完成数
 * 返回值: string - 周进度状态描述
 * 修改时间: 2025-07-18 18:30
 */
function calculateWeeklyProgress(weeklyTarget, completed) {
  if (!weeklyTarget || weeklyTarget <= 0) {
    return "⚪未设目标";
  }

  const completionRate = completed / weeklyTarget;

  if (completionRate >= 0.85) {
    return `🟢${Math.round(completionRate * 100)}%`;
  } else if (completionRate >= 0.7) {
    return `🟡${Math.round(completionRate * 100)}%`;
  } else {
    return `🔴${Math.round(completionRate * 100)}%`;
  }
}

// ===========================================
// 主要函数
// ===========================================

/**
 * 从PaaS获取综合看板数据
 *
 * 概述: 执行复杂的联合查询获取综合看板所需的实时数据
 * 调用的函数: queryPaaSData
 * 参数: currentMonth (string) - 当前月份
 * 返回值: array - 综合看板数据数组
 * 修改时间: 2025-07-18 18:30
 */
function fetchDashboardData(currentMonth) {
  // 构造复杂的联合查询SQL - 获取综合看板数据，包含本周目标汇总
  const sql = `
    SELECT TOP 20
      b.f_17f9bbed442c3ac1 AS 团队名称,
      bt.f_1981264b727f6692 AS 上月绩效,
      bt.f_19812654fa92ae27 AS 最终目标,
      bt.f_19812655f9882aa9 AS 已提交,
      bt.f_198126571a0e57ae AS 已完成,
      bt.f_1981265e829d925c AS 制衣完成,
      bt.f_1981265f6b9b9eb1 AS 毛织完成,
      bt.f_19812658f20e905f AS 制衣配额,
      bt.f_1981265c932555e8 AS 毛织配额,
      -- 计算实时完成率
      CASE
        WHEN bt.f_19812654fa92ae27 > 0
        THEN CAST((bt.f_198126571a0e57ae * 100.0 / bt.f_19812654fa92ae27) AS DECIMAL(5,2))
        ELSE 0
      END AS 实时完成率,
      -- 计算剩余配额
      (bt.f_19812654fa92ae27 - bt.f_19812655f9882aa9) AS 剩余配额,
      -- 计算本周目标汇总（通过子查询获取当前周的个人目标汇总）
      ISNULL((
        SELECT SUM(pt.f_197fe17af8feb9d1)
        FROM tb___f_13dafa2b1cfe13c5_197fe16de1ec2bab pt
        INNER JOIN tb___f_13dafa2b1cfe13c5 ks ON pt.f_sn = ks.f_sn
        WHERE ks.f_191e5a1de2002abe = b.f_sn
          AND pt.f_1981267aa59ee6ba = FORMAT(GETDATE(), 'yyyy-') + 'W' + CAST(DATEPART(wk, GETDATE()) AS VARCHAR(2))
      ), 0) AS 本周目标
    FROM ${CONFIG.PAAS_BRAND_ARCHIVE} b
    LEFT JOIN ${CONFIG.PAAS_BRAND_TARGET} bt ON b.f_sn = bt.f_sn
    WHERE bt.f_197f8636118bbb37 = '${currentMonth}'
    ORDER BY b.f_17f9bbed442c3ac1
  `;

  const result = queryPaaSData(sql);

  if (!result.success) {
    throw new Error(`PaaS综合看板数据查询失败: ${result.error}`);
  }

  logBuffer.push(
    `[INFO] 从PaaS获取到 ${result.data.length} 条 ${currentMonth} 的综合看板数据`
  );
  return result.data;
}

/**
 * 更新WPS综合看板表
 *
 * 概述: 将PaaS数据更新到WPS综合看板表格中
 * 调用的函数:
 *   - Application.Sheet.GetSheets
 *   - Application.Sheet.CreateSheet
 *   - Application.Record.GetRecords
 *   - Application.Record.UpdateRecords
 *   - Application.Record.CreateRecords
 * 参数: paasData (array) - PaaS系统返回的综合看板数据
 * 修改时间: 2025-07-18 18:30
 */
function updateDashboardTable(paasData) {
  try {
    // 查找或创建综合看板表格
    const sheets = Application.Sheet.GetSheets();
    let targetSheet = null;

    for (let i = 0; i < sheets.length; i++) {
      if (String(sheets[i].name).trim() === CONFIG.WPS_TARGET_TABLE) {
        targetSheet = sheets[i];
        break;
      }
    }

    // 如果表格不存在，创建新表格
    if (!targetSheet) {
      logBuffer.push(`[INFO] 创建新表格: ${CONFIG.WPS_TARGET_TABLE}`);

      const newSheet = Application.Sheet.CreateSheet({
        Name: CONFIG.WPS_TARGET_TABLE,
        Views: [{ name: "表", type: "Grid" }],
        Fields: [
          { name: "团队名称", type: "MultiLineText" },
          {
            name: "上月绩效",
            type: "SingleSelect",
            options: ["达标", "不达标"],
          },
          { name: "最终目标", type: "Number" },
          { name: "已提交", type: "Number" },
          { name: "已完成", type: "Number" },
          { name: "实时完成率", type: "Percentage" },
          { name: "制衣完成", type: "Number" },
          { name: "毛织完成", type: "Number" },
          { name: "本周目标", type: "Number" },
          { name: "周进度", type: "MultiLineText" },
          { name: "制衣配额", type: "Number" },
          { name: "毛织配额", type: "Number" },
          { name: "剩余配额", type: "Number" },
        ],
      });

      targetSheet = newSheet;
      logBuffer.push(`[INFO] 成功创建表格，ID: ${targetSheet.id}`);
    }

    // 获取现有记录
    const existingRecords = Application.Record.GetRecords({
      SheetId: Number(targetSheet.id),
    });

    // 处理每条PaaS数据
    const recordsToUpdate = [];
    const recordsToCreate = [];

    for (let i = 0; i < paasData.length; i++) {
      const paasRecord = paasData[i];

      // 查找是否存在相同团队的记录
      let existingRecord = null;
      for (let j = 0; j < existingRecords.length; j++) {
        const existingTeamName = String(
          existingRecords[j].fields["团队名称"] || ""
        ).trim();
        const paasTeamName = String(paasRecord.团队名称 || "").trim();

        if (existingTeamName === paasTeamName) {
          existingRecord = existingRecords[j];
          break;
        }
      }

      // 计算周进度状态
      const weeklyProgress = calculateWeeklyProgress(
        Number(paasRecord.本周目标 || 0),
        Number(paasRecord.已完成 || 0)
      );

      // 构造字段数据
      const fieldsData = {
        团队名称: String(paasRecord.团队名称 || ""),
        上月绩效: String(paasRecord.上月绩效 || "不达标"),
        最终目标: Number(paasRecord.最终目标 || 0),
        已提交: Number(paasRecord.已提交 || 0),
        已完成: Number(paasRecord.已完成 || 0),
        实时完成率: Number(paasRecord.实时完成率 || 0) / 100, // 转换为小数
        制衣完成: Number(paasRecord.制衣完成 || 0),
        毛织完成: Number(paasRecord.毛织完成 || 0),
        本周目标: Number(paasRecord.本周目标 || 0),
        周进度: weeklyProgress,
        制衣配额: Number(paasRecord.制衣配额 || 0),
        毛织配额: Number(paasRecord.毛织配额 || 0),
        剩余配额: Number(paasRecord.剩余配额 || 0),
      };

      if (existingRecord) {
        // 更新现有记录
        recordsToUpdate.push({
          recordId: existingRecord.id,
          fields: fieldsData,
        });
      } else {
        // 创建新记录
        recordsToCreate.push({
          fields: fieldsData,
        });
      }
    }

    // 执行更新操作
    if (recordsToUpdate.length > 0) {
      Application.Record.UpdateRecords({
        SheetId: Number(targetSheet.id),
        Records: recordsToUpdate,
      });
      logBuffer.push(`[INFO] 更新了 ${recordsToUpdate.length} 条现有记录`);
    }

    // 执行创建操作
    if (recordsToCreate.length > 0) {
      Application.Record.CreateRecords({
        SheetId: Number(targetSheet.id),
        Records: recordsToCreate,
      });
      logBuffer.push(`[INFO] 创建了 ${recordsToCreate.length} 条新记录`);
    }

    logBuffer.push(`[INFO] WPS综合看板表格更新完成`);
  } catch (error) {
    logBuffer.push(`[ERROR] WPS综合看板表格更新失败: ${error.message}`);
    throw error;
  }
}

/**
 * 主函数：执行从PaaS拉取综合看板数据
 *
 * 概述: 脚本的主要执行逻辑
 * 调用的函数:
 *   - getCurrentMonth
 *   - fetchDashboardData
 *   - updateDashboardTable
 *   - writeAirScriptLogsToWpsTable
 * 修改时间: 2025-07-18 18:30
 */
function main() {
  try {
    logBuffer.push(
      `[INFO] ${formatDateTime(new Date())} 开始执行PaaS综合看板数据拉取`
    );

    // 1. 获取当前月份
    const currentMonth = getCurrentMonth();
    logBuffer.push(`[INFO] 当前处理月份: ${currentMonth}`);

    // 2. 从PaaS获取综合看板数据
    logBuffer.push(`[INFO] 开始从PaaS拉取 ${currentMonth} 的综合看板数据`);
    const paasData = fetchDashboardData(currentMonth);

    if (paasData.length === 0) {
      logBuffer.push(`[WARN] PaaS系统中没有 ${currentMonth} 的综合看板数据`);
      return;
    }

    // 3. 更新WPS综合看板表格
    logBuffer.push(`[INFO] 开始更新WPS综合看板表格`);
    updateDashboardTable(paasData);

    logBuffer.push(`[INFO] 脚本执行成功完成`);
  } catch (error) {
    logBuffer.push(`[ERROR] 脚本执行失败: ${error.message}`);
    console.error(`脚本执行失败: ${error.message}`);
  } finally {
    // 记录执行日志到WPS表格
    const loggingConfig = {
      logBuffer: logBuffer,
      logTableName: CONFIG.WPS_LOG_TABLE,
      scriptStartTime: scriptStartTime,
    };

    const loggingResult = writeAirScriptLogsToWpsTable(loggingConfig);

    if (!loggingResult.success) {
      console.error(
        "严重错误: 脚本完成时未能将执行日志写入WPS表: " + loggingResult.error
      );
    }
  }
}

// 执行主函数
main();
