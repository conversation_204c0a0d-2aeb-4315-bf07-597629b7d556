# WPS 鉴权 (APIToken) | AirScript 文档

## 概述
本文档旨在详细介绍 WPS AirScript 中 API Token 的概念、作用、配置方法以及在脚本中的使用方式，帮助用户理解并正确使用 API Token 进行业务流程的鉴权操作。

## 什么是鉴权？
鉴权是指系统验证用户或应用程序是否有权限执行特定操作的过程。在 WPS AirScript 中，鉴权确保只有经过授权的脚本或流程才能访问敏感数据或执行关键业务操作，从而保障数据安全和系统稳定。

## WPS业务流程鉴权
WPS 业务流程中的鉴权通常涉及以下步骤：
1. 流程发起：业务流程启动。
2. 脚本调用：流程中的节点调用 AirScript 脚本。
3. API Token 获取：脚本在执行过程中需要调用外部服务或访问受保护资源时，通过配置的 API Token 进行身份认证。
4. 鉴权服务验证：外部服务或资源提供者验证 API Token 的有效性。
5. 授权访问：验证通过后，脚本获得执行后续操作的权限。
6. 结果返回：脚本执行结果返回给业务流程。

流程示意图（无法直接绘制，文字描述如下）：
[流程开始] -> [流程节点] -> [调用脚本] -> [脚本执行] -> [获取API Token] -> [发送API Token给外部服务] -> [外部服务验证API Token] --(验证成功)--> [授权访问/操作] -> [返回结果] -> [脚本执行完成] -> [流程结束]
''(涉及到具体流程图的细节，这部分需要用户根据文字描述自行理解或补充。)

## 如何配置鉴权？
在 WPS 中配置鉴权通常涉及以下步骤：
1. 打开 WPS 应用程序。
2. 进入相关的业务流程或自动化设置。
3. 找到 API Token 配置或密钥管理入口。（具体位置可能因 WPS 版本和配置而异）
4. 创建或导入 API Token。
5. 配置 Token 的权限和有效期。
6. 将配置好的 Token 关联到需要使用它的业务流程或脚本中。

（此处原文包含截图，截图内容大致展示了在 WPS 界面中进行 Token 配置的界面和选项。用户需要参照实际 WPS 界面进行操作。）

## 如何在脚本中使用？
在 AirScript 脚本中使用配置好的 API Token，可以通过特定的 AirScript API 或语法来获取和使用。通常，Token 会作为环境变量或可通过特定函数调用获取的配置项暴露给脚本。

以下是一个 AirScript 脚本使用 API Token 的示例（请注意，这只是一个示例，具体的 API 和语法可能需要参考最新的 AirScript 开发文档）：

```airscript
// 获取配置的 API Token
let apiToken = context.getApiToken("your_token_name"); // 假设通过一个API获取

// 或者从环境变量中读取 (如果Token被配置为环境变量)
// let apiToken = system.getenv("WPS_API_TOKEN");

// 检查 Token 是否获取成功
if (apiToken) {
    // 使用获取到的 Token 调用外部服务
    let response = http.post("https://your_external_service.com/api/data", {
        headers: {
            "Authorization": "Bearer " + apiToken
        },
        body: {
            // 请求体数据
        }
    });

    // 处理响应
    if (response.status == 200) {
        console.log("数据获取成功: " + response.body);
    } else {
        console.error("调用失败: " + response.status);
    }
} else {
    console.error("API Token 未获取到。");
}
```

**关键点：**
*   使用 AirScript 提供的功能（如 `context.getApiToken` 或环境变量）来安全地获取 API Token，而不是硬编码在脚本中。
*   将 API Token 放在 HTTP 请求的 `Authorization` 头中，通常使用 `Bearer` 方案。
*   妥善处理获取 Token 失败或调用外部服务失败的情况。

## API Token 泄露了怎么办？
如果您的 API Token 不慎泄露，应立即采取以下措施：
1. **立即吊销泄露的 Token：** 在 WPS 的 Token 管理界面找到对应的 Token，并立即执行吊销操作，使其失效。
2. **生成新的 Token：** 吊销旧 Token 后，立即生成一个新的 API Token 用于后续操作。
3. **更新所有使用该 Token 的地方：** 检查所有使用了泄露 Token 的业务流程和 AirScript 脚本，将其更新为新生成的 Token。
4. **排查泄露原因：** 尽力查明 Token 泄露的原因，例如是否是代码管理不当、存储不安全等，并采取措施防止再次发生。
5. **通知相关方：** 如果 Token 的泄露可能影响到第三方服务或用户，应及时通知相关方。

## 最佳实践
*   **最小权限原则：** 为 API Token 分配所需的最小权限，避免赋予不必要的访问能力。
*   **设置合理的有效期：** 为 Token 设置一个合理的有效期，并定期更新。
*   **安全存储和管理：** 避免将 Token 硬编码在代码中，使用 WPS 提供的安全存储或环境变量来管理 Token。
*   **监控和审计：** 定期监控 Token 的使用情况，并进行审计，及时发现异常。
*   **使用 HTTPS：** 在调用外部服务时，始终使用 HTTPS 协议，确保数据传输的安全性。

## 注意事项
*   **不要在公共代码库中硬编码 Token：** 这会极大地增加 Token 泄露的风险。
*   **不要通过不安全的渠道分享 Token：** 例如通过邮件、聊天工具等明文传输 Token。
*   **定期审查 Token 的使用和权限：** 确保 Token 的权限仍然符合需求，并及时移除不再需要的 Token。
*   **关注 WPS 官方的安全更新和最佳实践建议：** 及时了解和采纳官方发布的最新安全信息和建议。

这份文档应该包含了图片中的主要信息，并按照 Markdown 格式进行了整理。您可以查看 `docs/WPS_AirScript_API_Token_说明.md` 文件来确认内容。 