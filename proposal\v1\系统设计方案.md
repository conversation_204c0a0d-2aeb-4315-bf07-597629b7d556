# 产品开发计划性管理系统设计方案

## 技术架构概述

### 系统分工体系

#### PaaS系统职责

- **业务流程管理**：承载核心业务流程，记录审品、打版等业务操作
- **原始数据存储**：保存审品表、品牌档案、款系列档案等业务原始数据
- **目标计划管理**：记录品牌月度目标、个人月度目标等计划数据
- **数据API服务**：提供数据查询接口，供WPS系统调用获取数据

#### WPS系统职责

- **数据应用平台**：通过API获取PaaS系统数据，进行数据处理和展示
- **数据可视化展示**：设计管理看板、个人看板、实时大屏等数据可视化界面
- **个人周度管理**：团队长在WPS内完成个人周度目标设定和配额分配
- **数据回传更新**：通过JavaScript/Python脚本调用PaaS接口，上传个人周度管理数据

### 技术栈

- **数据存储**: SQL Server (PaaS系统)
- **数据可视化**: WPS多维表格
- **数据处理**: JavaScript + Python
- **数据同步**: API接口调用

### 系统架构图

```mermaid
graph TB
    A[PaaS系统<br/>SQL Server] --> B[API接口]
    B --> C[WPS多维表格]
    C --> D[实时竞争大屏]
    C --> E[团队管理看板]
    C --> F[个人周度管理界面]
  
    F --> G[JavaScript/Python脚本]
    G --> H[数据回传API]
    H --> A
  
    I[自动任务] --> J[数据计算]
    J --> K[等级评定]
    K --> L[配额分配]
    L --> M[预警生成]
  
    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style F fill:#fff3e0
    style I fill:#e8f5e8
```

---

## 数据库扩展设计

### 现有表结构分析

基于您提供的表结构，我们有以下核心表：

1. **审品表** (`tb___f_8b77100808d187dd`) - 核心业务表

   - 包含审品通过状态：`f_17f9acb6930a59ec AS [状态]` (状态="通过"表示审品通过)
   - 包含产品类型：`f_1851ef89d51e7338 AS [产品类型]` (可用于区分制衣/毛织)
   - 包含款式编码：`f_1851f0193c15983f AS [款式编码]` (关联到款系列档案)
   - 包含紧急程度：`f_1821766bbbed849c AS [紧急程度]` (用于标识加急申请)
   - 通过款式编码关联到款系列档案，获取开款人信息进行个人跟踪
2. **品牌档案** (`tb___f_e624233ca743e567`)
3. **品牌档案.产品开发目标** (`tb___f_e624233ca743e567_197f862cf351062f`)
4. **款系列档案** (`tb___f_13dafa2b1cfe13c5`)
5. **款系列档案.产品开发目标** (`tb___f_13dafa2b1cfe13c5_197fe16de1ec2bab`)
6. **基础参数表** (`tb___f_5dbd46c6c00cfd00`) - 用于系统配置

### 业务关系分析

**核心业务关系：**

- **团队 = 品牌**：每个品牌就是一个团队
- **个人 = 开款人**：款系列档案中的开款人就是个人维度
- **关系链**：品牌（团队）→ 款系列档案 → 开款人（个人）

**实际业务流程：**
打版申请（增加加急按钮）→ 审品 → 审品通过 → 占用名额

**数据关联逻辑：**

```
审品表 → 款式编码 → 款系列档案 → 品牌（团队）
审品表.f_1851f0193c15983f → 款系列档案.f_180c65f51ae34c1a → 款系列档案.f_191e5a1de2002abe → 品牌档案
审品表.f_1851f0193c15983f → 款系列档案.f_180c65f51ae34c1a → 款系列档案.f_189aa970458aad59 → 开款人（个人）
```

### 加急权限设计

**设计方案：** 在PAAS系统的【打版申请】功能中增加"加急申请"按钮，供团队负责人点击使用。

**实现逻辑：**

1. **打版申请界面**：增加"加急申请"按钮，仅团队负责人可见
2. **权限控制**：按团队等级限制加急次数（A级15%、B级10%、C级5%、D级0%）
3. **状态传递**：加急状态在审品表中通过 `f_1821766bbbed849c AS [紧急程度]`字段记录
4. **全流程传递**：该加急状态将传递到后续所有相关业务表中

---

## 功能模块设计

### 模块一：品牌团队配额管理

#### 1.1 数据表扩展

**主表：** 品牌档案 (`tb___f_e624233ca743e567`)
**子表名：** 品牌档案.团队配额管理
**用途：** 在品牌档案下管理各团队的月度配额和等级

| 字段名                  | 类型          | 说明               |
| ----------------------- | ------------- | ------------------ |
| f_month                 | NVARCHAR(10)  | 月份 (YYYY-MM)     |
| f_team_name             | NVARCHAR(100) | 团队名称           |
| f_last_month_completion | DECIMAL(5,2)  | 上月完成率         |
| f_team_level            | NVARCHAR(10)  | 团队等级 (A/B/C/D) |
| f_base_quota            | INT           | 基础配额           |
| f_actual_quota          | INT           | 实际配额           |
| f_clothing_quota        | INT           | 制衣配额           |
| f_knitting_quota        | INT           | 毛织配额           |
| f_urgent_quota          | INT           | 加急次数配额       |
| f_used_quota            | INT           | 已用配额           |
| f_used_clothing         | INT           | 已用制衣配额       |
| f_used_knitting         | INT           | 已用毛织配额       |
| f_used_urgent           | INT           | 已用加急次数       |
| f_quota_utilization     | DECIMAL(5,2)  | 配额利用率         |
| f_clothing_utilization  | DECIMAL(5,2)  | 制衣配额利用率     |
| f_knitting_utilization  | DECIMAL(5,2)  | 毛织配额利用率     |
| f_clothing_target       | INT           | 制衣目标           |
| f_knitting_target       | INT           | 毛织目标           |
| f_total_target          | INT           | 总目标             |

#### 1.2 配额计算逻辑

**配额计算公式：**

```
基础配额 = 品牌月度目标 × 基础系数
实际配额 = 基础配额 × 等级系数
制衣配额 = 实际配额 × 制衣目标占比
毛织配额 = 实际配额 × 毛织目标占比
加急次数配额 = 实际配额 × 加急比例
```

**配额利用率计算公式：**

```
配额利用率 = 总目标 ÷ 实际配额 × 100%
制衣配额利用率 = 制衣目标 ÷ 制衣配额 × 100%
毛织配额利用率 = 毛织目标 ÷ 毛织配额 × 100%
```

**等级系数配置：**

| 等级 | 系数 | 加急比例 | 说明              |
| ---- | ---- | -------- | ----------------- |
| A级  | 1.2  | 15%      | 上月完成率 ≥95%  |
| B级  | 1.0  | 10%      | 上月完成率 85-94% |
| C级  | 0.8  | 5%       | 上月完成率 70-84% |
| D级  | 0.5  | 0%       | 上月完成率 <70%   |

#### 1.3 配额分配算法

**执行时间：** 每月1号自动执行
**计算步骤：**

1. **获取上月完成率**
   通过品牌档案关联产品开发目标和审品表，计算各品牌上月完成率：

   - 从品牌档案.产品开发目标表获取上月目标数量
   - 从审品表统计上月状态为"通过"的完成数量
   - 计算完成率：上月完成数量 ÷ 上月目标数量 × 100%
2. **确定等级**
   根据上月完成率确定团队等级：

   - 完成率 ≥ 95%：A级（优秀团队）
   - 完成率 85-94%：B级（良好团队）
   - 完成率 70-84%：C级（需改进团队）
   - 完成率 < 70%：D级（重点关注团队）
3. **计算配额**
   根据团队等级和本月目标计算实际配额：

   - **实际配额计算**：本月目标 × 等级系数
   - **制衣/毛织配额分配**：根据制衣/毛织目标占比进行配额分配
   - **加急配额计算**：实际配额 × 加急比例
4. **计算配额利用率**
   根据目标与配额的关系计算利用率：

   - **总配额利用率**：总目标 ÷ 实际配额 × 100%
   - **制衣配额利用率**：制衣目标 ÷ 制衣配额 × 100%
   - **毛织配额利用率**：毛织目标 ÷ 毛织配额 × 100%

#### 1.4 配额使用统计

**统计规则：** 审品状态="通过" = 占用配额

**统计逻辑：**
通过品牌档案关联团队配额管理和审品表，统计各品牌的配额使用情况：

- **实际配额**：从团队配额管理表获取当月实际配额
- **制衣配额**：从团队配额管理表获取当月制衣配额
- **毛织配额**：从团队配额管理表获取当月毛织配额
- **加急配额**：从团队配额管理表获取当月加急配额
- **已用配额**：统计审品表中该品牌当月状态为"通过"的记录数
- **已用制衣**：统计审品表中该品牌当月产品类型为制衣且状态为"通过"的记录数
- **已用毛织**：统计审品表中该品牌当月产品类型为毛织且状态为"通过"的记录数
- **已用加急**：统计审品表中该品牌当月紧急程度为"加急"且状态为"通过"的记录数
- **剩余配额**：实际配额减去已用配额
- **剩余制衣配额**：制衣配额减去已用制衣
- **剩余毛织配额**：毛织配额减去已用毛织
- **剩余加急**：加急配额减去已用加急
- **配额使用率**：已用配额除以实际配额，计算使用百分比
- **制衣配额使用率**：已用制衣除以制衣配额，计算使用百分比
- **毛织配额使用率**：已用毛织除以毛织配额，计算使用百分比

#### 1.5 预警机制

**监控指标：**

- 总配额使用率（已用配额/实际配额）
- 制衣配额使用率（已用制衣/制衣配额）
- 毛织配额使用率（已用毛织/毛织配额）
- 加急使用率（已用加急/加急配额）
- 配额利用率（目标/配额）
- 日均使用速度（已用配额/当月已过天数）

**预警规则：**

| 预警级别 | 触发条件          | 预警动作                   |
| -------- | ----------------- | -------------------------- |
| 黄色预警 | 配额使用率 ≥ 80% | 系统提醒，大屏显示黄色     |
| 橙色预警 | 配额使用率 ≥ 90% | 系统警告，大屏显示橙色     |
| 红色预警 | 配额使用率 ≥ 95% | 系统告警，大屏显示红色闪烁 |
| 配额耗尽 | 配额使用率 = 100% | 停止接受新申请（A级除外）  |
| 利用率预警 | 配额利用率 > 120% | 目标过高，建议调整目标设定 |
| 利用率预警 | 配额利用率 < 80% | 配额冗余，建议优化配额分配 |

#### 1.6 WPS数据可视化展示

##### 1.6.1 实时竞争大屏

**WPS表名**: `实时竞争大屏`
**数据来源**: 通过Python脚本从PaaS系统汇总计算后推送到WPS
**更新频率**: 每小时自动更新
**展示目的**: 为管理层提供团队竞争态势的实时监控

| 字段名         | 字段类型 | 数据来源                | 计算逻辑                             |
| -------------- | -------- | ----------------------- | ------------------------------------ |
| 团队名称       | 文本     | 品牌档案.团队配额管理   | 直接获取团队名称                     |
| 当前等级       | 文本     | 品牌档案.团队配额管理   | 等级+趋势图标                        |
| 本月目标       | 数字     | 款系列档案.产品开发目标 | 按团队汇总月度目标                   |
| 已提交         | 数字     | 审品表                  | 统计团队当月打版申请状态≠淘汰记录数 |
| 已完成         | 数字     | 审品表                  | 统计团队当月状态='通过'记录数        |
| 实时完成率     | 百分比   | 计算字段                | 已完成/本月目标*100%                 |
| 制衣完成       | 文本     | 审品表                  | 制衣类型且状态='通过'的数量及占比    |
| 毛织完成       | 文本     | 审品表                  | 毛织类型且状态='通过'的数量及占比    |
| 本周目标       | 数字     | 款系列档案.个人周度管理 | 按团队汇总周度目标                   |
| 周度进度       | 文本     | 计算字段                | 周度完成率+预警状态                  |
| 剩余配额       | 数字     | 计算字段                | 实际配额-已用配额                    |
| 剩余制衣配额   | 数字     | 计算字段                | 制衣配额-已用制衣                    |
| 剩余毛织配额   | 数字     | 计算字段                | 毛织配额-已用毛织                    |
| 加急剩余       | 数字     | 计算字段                | 加急配额-已用加急                    |
| 配额利用率     | 百分比   | 计算字段                | 总目标/实际配额*100%                 |
| 制衣配额利用率 | 百分比   | 计算字段                | 制衣目标/制衣配额*100%               |
| 毛织配额利用率 | 百分比   | 计算字段                | 毛织目标/毛织配额*100%               |

**WPS展示特色：**

- 使用WPS多维表格的条件格式功能，根据完成率自动调整行颜色
- 配额使用率超过90%的团队自动高亮显示
- 配额利用率异常（>120%或<80%）的团队特殊标识
- 支持按等级、完成率、配额使用率、配额利用率等多维度排序
- 实时刷新，确保数据时效性

##### 1.6.2 团队管理看板

**WPS表名**: `团队管理看板`
**数据来源**: 通过JavaScript脚本从PaaS系统按开款人汇总后推送到WPS
**更新频率**: 每日自动更新
**展示目的**: 为团队长提供团队成员管理的详细视图

| 字段名         | 字段类型 | 数据来源                | 计算逻辑                                |
| -------------- | -------- | ----------------------- | --------------------------------------- |
| 负责人         | 文本     | 款系列档案              | 获取开款人姓名                          |
| 款系列         | 文本     | 款系列档案              | 获取款系列名称                          |
| 本月目标       | 数字     | 款系列档案.产品开发目标 | 制衣目标+毛织目标                       |
| 已完成         | 数字     | 审品表                  | 统计开款人当月状态='通过'记录数         |
| 完成率         | 百分比   | 计算字段                | 已完成/本月目标*100%                    |
| 制衣完成       | 文本     | 审品表                  | 开款人制衣类型且状态='通过'的数量及占比 |
| 毛织完成       | 文本     | 审品表                  | 开款人毛织类型且状态='通过'的数量及占比 |
| 本周目标       | 数字     | 款系列档案.个人周度管理 | 周度目标                                |
| 周度进度       | 文本     | 计算字段                | 周度完成率+预警状态                     |
| 建议等级       | 文本     | 款系列档案.个人周度管理 | 系统建议等级                            |
| 周配额利用率   | 百分比   | 计算字段                | 周目标/周配额*100%                      |
| 制衣配额利用率 | 百分比   | 计算字段                | 制衣目标/制衣配额*100%                  |
| 毛织配额利用率 | 百分比   | 计算字段                | 毛织目标/毛织配额*100%                  |

**WPS展示特色：**

- 使用WPS的筛选功能，支持按团队、等级、完成率等条件筛选
- 集成WPS的图表功能，生成团队完成率分布图
- 支持导出功能，便于团队长制作汇报材料
- 可设置提醒功能，完成率异常时自动通知团队长
- 配额利用率异常时自动高亮显示并提供调整建议

#### 1.7 团队配额管理技术实施

##### 1.7.1 数据同步机制

**数据获取策略：**

- **实时大屏数据**：每小时从PaaS系统SQL Server获取最新审品数据
- **团队管理数据**：每日获取团队完成情况和配额使用数据

**技术实施思路：**

1. **数据提取**：使用Python脚本通过API从PaaS系统获取团队配额状态数据
2. **数据计算**：计算实时完成率、配额使用率等关键指标
3. **数据推送**：将处理后的数据通过WPS API推送到相应表格
4. **异常处理**：建立数据同步失败的重试机制和错误日志记录

##### 1.7.2 WPS多维表格功能应用

**条件格式设计：**

- **完成率条件格式**：根据完成率区间自动设置不同颜色背景（绿色≥100%、黄色80-99%、橙色60-79%、红色<60%）
- **配额使用率条件格式**：根据使用率设置预警颜色（红色闪烁≥95%、橙色80-94%、黄色60-79%、绿色<60%）

**图表可视化方案：**

- **团队完成率排行榜**：使用柱状图展示各团队完成率排名，便于竞争对比
- **配额使用率分布**：使用饼图展示整体配额使用情况，直观显示剩余配额
- **完成率趋势图**：使用折线图展示团队完成率的时间趋势变化
- **等级分布图**：使用环形图展示各等级团队的分布情况

**交互功能设计：**

- 支持按等级、完成率、配额使用率等多维度排序和筛选
- 提供数据透视表功能，支持多维度数据分析
- 集成导出功能，便于生成管理报告

### 模块二：个人周度管理

#### 2.1 数据表扩展

**主表：** 款系列档案 (`tb___f_13dafa2b1cfe13c5`)
**子表名：** 款系列档案.个人周度管理
**用途：** 在款系列档案下管理开款人的周度数据和建议
**数据来源：** WPS系统通过API回传到PaaS系统

| 字段名                 | 类型         | 说明           | 数据来源                               |
| ---------------------- | ------------ | -------------- | -------------------------------------- |
| f_year                 | NVARCHAR(20) | 月份 (YYYY-MM) | PAAS系统计算                           |
| f_week                 | INT          | 周次 (1-53)    | PAAS系统通过日期计算，无需"周"字段     |
| f_last_week_completion | DECIMAL(5,2) | 上周完成率     | PAAS系统计算                           |
| f_suggest_level        | NVARCHAR(20) | 建议等级       | PAAS系统计算                           |
| f_week_target          | INT          | 周目标         | WPS系统团队长设定，用API上传回PAAS系统 |
| f_clothing_target      | INT          | 制衣目标       | WPS系统团队长设定，用API上传回PAAS系统 |
| f_knitting_target      | INT          | 毛织目标       | WPS系统团队长设定，用API上传回PAAS系统 |
| f_clothing_completed   | INT          | 制衣完成       | PAAS系统计算                           |
| f_knitting_completed   | INT          | 毛织完成       | PAAS系统计算                           |
| f_week_quota           | INT          | 周配额         | WPS系统团队长分配，用API上传回PAAS系统 |
| f_clothing_quota       | INT          | 制衣配额       | WPS系统团队长分配，用API上传回PAAS系统 |
| f_knitting_quota       | INT          | 毛织配额       | WPS系统团队长分配，用API上传回PAAS系统 |
| f_quota_utilization    | DECIMAL(5,2) | 配额利用率     | PAAS系统计算                           |
| f_clothing_utilization | DECIMAL(5,2) | 制衣配额利用率 | PAAS系统计算                           |
| f_knitting_utilization | DECIMAL(5,2) | 毛织配额利用率 | PAAS系统计算                           |

**周次计算说明：**
由于PAAS系统没有"周"字段，通过以下方式计算：

```sql
-- 获取年份
YEAR(审品表.创建时间) AS f_year
-- 获取周次（ISO周，1-53）
DATEPART(WEEK, 审品表.创建时间) AS f_week
```

#### 2.2 个人配额分配逻辑

**分配原则：**

- 基于开款人上周完成率生成建议等级
- 团队长可参考建议进行灵活调整
- 在团队配额范围内进行个人配额分配

**建议等级计算：**
通过款系列档案关联个人周度管理和审品表，计算开款人上周完成率和建议等级：

- **数据获取**：从个人周度管理表获取上周目标，从审品表统计上周完成数量
- **完成率计算**：上周完成数量 ÷ 上周目标 × 100%
- **等级判定**：根据完成率确定建议等级
  - 完成率 ≥ 90%：优秀
  - 完成率 80-89%：良好
  - 完成率 60-79%：需改进
  - 完成率 < 60%：重点关注

#### 2.3 个人数据统计

**统计规则：** 开款人审品状态="通过" = 占用个人配额

**统计逻辑：**
通过款系列档案关联个人周度管理和审品表，统计各开款人的配额使用情况：

- **周配额**：从个人周度管理表获取当周总配额
- **制衣配额**：从个人周度管理表获取当周制衣配额
- **毛织配额**：从个人周度管理表获取当周毛织配额
- **已用配额**：统计审品表中该开款人当周状态为"通过"的记录数
- **制衣已用**：统计审品表中该开款人当周产品类型为制衣且状态为"通过"的记录数
- **毛织已用**：统计审品表中该开款人当周产品类型为毛织且状态为"通过"的记录数
- **剩余配额**：周配额减去已用配额
- **剩余制衣配额**：制衣配额减去制衣已用
- **剩余毛织配额**：毛织配额减去毛织已用
- **配额使用率**：已用配额除以周配额，计算使用百分比
- **配额利用率**：周目标除以周配额，计算利用百分比
- **制衣配额利用率**：制衣目标除以制衣配额，计算利用百分比
- **毛织配额利用率**：毛织目标除以毛织配额，计算利用百分比

#### 2.4 WPS系统管理流程

**执行时间：** 每周一自动生成建议，团队长在WPS内完成管理
**分配步骤：**

1. **WPS系统生成建议**

   - 从PaaS系统获取上周完成数据
   - 计算各开款人上周完成率
   - 生成建议等级和配额调整建议
   - 在WPS个人周度管理表中显示建议
2. **团队长在WPS内管理**

   - 在WPS个人周度管理表中查看系统建议
   - 结合实际情况设定个人周度目标
   - 分配个人周度配额（总配额、制衣配额、毛织配额）
   - 填写管理备注
3. **WPS系统验证和同步**

   - 验证个人配额总和不超过团队配额
   - 验证制衣/毛织配额分配合理性
   - 通过API将管理数据同步到PaaS系统
   - 生成配额分配报告

#### 2.5 WPS个人周度管理界面

**WPS表名**: `个人周度管理`
**数据来源**: WPS系统综合计算，团队长操作管理
**更新频率**: 每周一自动更新建议数据，团队长实时编辑
**功能定位**: 团队长进行个人周度目标设定和配额分配的核心管理界面

| 字段名       | 字段类型 | 数据来源    | 功能描述                     |
| ------------ | -------- | ----------- | ---------------------------- |
| 开款人       | 文本     | 款系列档案  | 显示开款人姓名（只读）       |
| 款系列       | 文本     | 款系列档案  | 显示款系列名称（只读）       |
| 上周完成率   | 百分比   | WPS系统计算 | 显示上周完成率（只读）       |
| 建议等级     | 文本     | WPS系统计算 | 显示系统建议等级（只读）     |
| 本周目标     | 数字     | 团队长设定  | 团队长设定本周目标（可编辑） |
| 制衣目标     | 数字     | 团队长设定  | 团队长设定制衣目标（可编辑） |
| 毛织目标     | 数字     | 团队长设定  | 团队长设定毛织目标（可编辑） |
| 本周配额     | 数字     | 团队长分配  | 团队长分配本周配额（可编辑） |
| 制衣配额     | 数字     | 团队长分配  | 团队长分配制衣配额（可编辑） |
| 毛织配额     | 数字     | 团队长分配  | 团队长分配毛织配额（可编辑） |
| 配额调整建议 | 文本     | WPS系统计算 | 显示配额调整建议（只读）     |
| 管理备注     | 文本     | 团队长填写  | 团队长填写管理备注（可编辑） |

**WPS界面特色功能：**

1. **智能数据验证**

   - 使用WPS的数据验证功能，确保目标设定合理
   - 配额分配总和不能超过团队配额，实时提醒
   - 制衣+毛织目标/配额应等于总目标/配额
2. **交互式管理体验**

   - 支持批量设置功能，快速完成团队配额分配
   - 提供配额分配模板，基于建议等级自动填充
   - 集成计算器功能，辅助团队长进行配额计算
3. **数据同步机制**

   - 提供"一键同步"按钮，将设置数据推送到PaaS系统
   - 同步前进行数据完整性检查
   - 同步后显示确认信息和生成分配报告
4. **可视化辅助**

   - 使用WPS图表功能，显示团队配额分配饼图
   - 展示个人完成率趋势图，辅助决策
   - 提供配额使用率预警，避免超额分配

#### 2.6 个人周度管理技术实施

##### 2.6.1 数据获取与建议生成

**数据获取策略：**

- **个人表现数据**：每周从PaaS系统获取各开款人的完成情况
- **建议等级计算**：根据上周完成率自动计算建议等级和配额调整建议

**建议生成逻辑：**

1. **数据收集**：从PaaS系统获取个人上周完成数据
2. **完成率计算**：计算各开款人的上周完成率
3. **等级判定**：根据完成率区间确定建议等级（优秀≥90%、良好80-89%、需改进60-79%、重点关注<60%）
4. **配额建议**：根据建议等级生成配额调整建议和预警状态
5. **数据推送**：将建议数据推送到WPS个人周度管理表

##### 2.6.2 数据回传机制

**数据同步策略：**

- **触发时机**：团队长完成个人周度管理设置后，点击"同步到PaaS系统"按钮
- **验证机制**：同步前进行数据完整性和合理性验证
- **同步确认**：同步完成后显示结果确认和生成分配报告

**数据验证逻辑：**

1. **配额总量验证**：确保个人配额分配总和不超过团队配额
2. **配额结构验证**：确保制衣配额+毛织配额等于总配额
3. **目标合理性验证**：确保目标设定在合理范围内
4. **数据完整性验证**：确保必填字段完整且格式正确

**同步流程设计：**

1. **数据收集**：从WPS个人周度管理表收集所有设置数据
2. **数据验证**：执行多层次数据验证，确保数据质量
3. **格式转换**：将WPS表格数据转换为PaaS系统API所需格式
4. **API调用**：通过HTTP请求将数据推送到PaaS系统
5. **结果处理**：接收PaaS系统返回结果，更新WPS中的同步状态

##### 2.6.3 可视化图表功能

**个人管理图表设计：**

- **个人完成率趋势图**：使用折线图展示个人完成率的周度变化趋势，包含目标线对比
- **配额分配结构图**：使用环形图展示团队配额在各个人之间的分配结构
- **制衣/毛织完成对比**：使用双轴图对比个人在制衣和毛织方面的完成情况
- **个人完成率对比**：使用雷达图展示团队内各成员的多维度表现对比

**交互功能设计：**

- **批量设置功能**：支持选择多个开款人进行批量配额设置
- **配额分配模板**：提供基于建议等级的自动配额分配模板
- **实时计算器**：集成配额计算辅助工具，帮助团队长进行配额分配
- **历史数据参考**：提供历史配额分配比例参考，辅助决策

**用户体验优化：**

- **智能提醒**：配额分配异常时自动弹出提醒和建议
- **进度显示**：数据同步过程中显示进度条和状态信息
- **操作确认**：重要操作前提供确认对话框，避免误操作
- **快捷操作**：提供快捷键和右键菜单，提升操作效率

### 模块三：系统配置管理

#### 3.1 配置表设计

**主表：** 品牌档案 (`tb___f_e624233ca743e567`)
**子表名：** 品牌档案.系统配置
**用途：** 存储系统配置参数

| 字段名         | 类型          | 说明   |
| -------------- | ------------- | ------ |
| f_config_name  | NVARCHAR(50)  | 配置名 |
| f_config_value | DECIMAL(5,2)  | 配置值 |
| f_description  | NVARCHAR(200) | 描述   |

**初始配置数据：**

| f_config_name   | f_config_value | f_description         |
| --------------- | -------------- | --------------------- |
| A级配额系数     | 1.2            | A级团队配额计算系数   |
| B级配额系数     | 1.0            | B级团队配额计算系数   |
| C级配额系数     | 0.8            | C级团队配额计算系数   |
| D级配额系数     | 0.5            | D级团队配额计算系数   |
| A级加急比例     | 0.15           | A级团队加急配额比例   |
| B级加急比例     | 0.10           | B级团队加急配额比例   |
| C级加急比例     | 0.05           | C级团队加急配额比例   |
| D级加急比例     | 0.00           | D级团队加急配额比例   |
| A级完成率阈值   | 95.00          | A级团队完成率最低要求 |
| B级完成率最低值 | 85.00          | B级团队完成率最低要求 |
| B级完成率最高值 | 94.99          | B级团队完成率最高范围 |
| C级完成率最低值 | 70.00          | C级团队完成率最低要求 |
| C级完成率最高值 | 84.99          | C级团队完成率最高范围 |

#### 3.2 配置使用方式

**配置读取示例：**

```sql
-- 获取A级配额系数
SELECT f_config_value FROM 品牌档案.系统配置 WHERE f_config_name = 'A级配额系数';

-- 计算团队配额（示例）
SELECT 
    品牌名称,
    月度目标,
    月度目标 * (SELECT f_config_value FROM 品牌档案.系统配置 WHERE f_config_name = 团队等级 + '级配额系数') AS 实际配额
FROM 品牌档案.团队配额管理;

-- 根据完成率判断等级
SELECT 
    CASE 
        WHEN 完成率 >= (SELECT f_config_value FROM 品牌档案.系统配置 WHERE f_config_name = 'A级完成率阈值') THEN 'A'
        WHEN 完成率 >= (SELECT f_config_value FROM 品牌档案.系统配置 WHERE f_config_name = 'B级完成率最低值') THEN 'B'
        WHEN 完成率 >= (SELECT f_config_value FROM 品牌档案.系统配置 WHERE f_config_name = 'C级完成率最低值') THEN 'C'
        ELSE 'D'
    END AS 团队等级
FROM 品牌档案.团队配额管理;
```

#### 3.3 配置优势

**使用配置表的优势：**

- **简单直接**：一个表存储所有配置，结构清晰
- **易于维护**：直接修改表数据即可更新配置
- **实时生效**：配置修改后立即生效，无需重启
- **可视化管理**：可以通过界面直接查看和修改配置
- **历史追踪**：可以轻松查看配置变更历史

**配置修改方式：**

```sql
-- 修改配置示例
UPDATE 品牌档案.系统配置 SET f_config_value = 1.3 WHERE f_config_name = 'A级配额系数';
UPDATE 品牌档案.系统配置 SET f_config_value = 96.0 WHERE f_config_name = 'A级完成率阈值';
```

### 模块四：等级评定系统

#### 4.1 团队等级评定

**评定维度：** 上月完成率
**评定周期：** 每月1号自动评定

**等级评定算法：**

1. **获取上月数据**
   通过品牌档案关联产品开发目标和审品表，获取各品牌上月完成情况：

   - 从品牌档案.产品开发目标表获取上月目标数量
   - 从审品表统计上月状态为"通过"的完成数量
   - 计算上月完成率：完成数量 ÷ 目标数量 × 100%
2. **计算等级**
   根据上月完成率使用配置数组中的阈值计算本月等级：

   - 完成率 ≥ 95%：A级（优秀团队）
   - 完成率 85%-94.99%：B级（良好团队）
   - 完成率 70%-84.99%：C级（需改进团队）
   - 完成率 < 70%：D级（重点关注团队）

   使用程序中的配置数组自动判定等级
3. **等级变化分析**
   对比本月等级与上月等级，分析等级变化趋势：

   - **升级情况**：本月等级 > 上月等级，显示"↗️升级"
   - **降级情况**：本月等级 < 上月等级，显示"↘️降级"
   - **保持情况**：本月等级 = 上月等级，显示"➡️保持"

#### 4.2 个人建议等级评定

**评定维度：** 上周完成率
**评定周期：** 每周一自动评定

**个人建议等级算法：**

1. **获取上周数据**
   通过款系列档案关联品牌档案、个人周度管理和审品表，获取各开款人上周完成情况：

   - 从个人周度管理表获取上周目标、制衣目标、毛织目标
   - 从审品表统计上周状态为"通过"的完成数量
   - 分别统计制衣和毛织的完成数量
   - 计算上周完成率：完成数量 ÷ 目标数量 × 100%
2. **计算建议等级**
   根据上周完成率计算建议等级和配额调整建议：

   - **优秀（≥90%）**：建议等级"优秀"，配额调整建议"可考虑增加20%"，预警状态"🟢正常"
   - **良好（80-89%）**：建议等级"良好"，配额调整建议"保持标准配额"，预警状态"🟢正常"
   - **需改进（60-79%）**：建议等级"需改进"，配额调整建议"可考虑减少20%"，预警状态"🟡预警"
   - **重点关注（<60%）**：建议等级"重点关注"，配额调整建议"可考虑减少50%"，预警状态"🔴警告"

### 模块五：自动化任务调度

#### 5.1 数据同步任务

**每小时数据更新**

- 任务频率：每小时执行
- 更新内容：从审品表获取最新数据、计算团队实时完成率、更新WPS实时竞争大屏、检查配额使用情况

**每日等级计算**

- 任务频率：每天8点执行
- 计算内容：计算团队上月完成率、更新团队等级、计算个人上周完成率、更新个人建议等级

#### 5.2 配额管理任务

**月度配额分配**

- 任务频率：每月1号执行
- 分配逻辑：根据团队上月完成率确定等级、按等级系数计算实际配额、计算加急配额、重置已用配额计数

**周度配额建议**

- 任务频率：每周一执行
- 建议逻辑：根据个人上周完成率生成建议等级、为团队长提供配额分配建议、更新个人周度管理数据

#### 5.3 预警系统任务

**配额预警检查**

- 检查频率：每小时执行
- 预警条件：团队配额使用率超过90%、个人配额使用率超过90%、周度完成率低于预期

**进度预警检查**

- 检查频率：每日执行
- 预警条件：周度完成率低于60%、月度完成率严重偏离目标、等级即将下降
