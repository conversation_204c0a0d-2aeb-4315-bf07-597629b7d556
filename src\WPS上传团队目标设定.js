/**
 * 脚本名称：WPS上传团队目标设定到PaaS
 *
 * 功能描述：将WPS中用户设定的团队目标数据上传到PaaS系统
 * 数据流向：WPS团队月度目标设定表 → PaaS品牌档案.产品开发目标表
 * 使用接口：/zijiezhen/manage (更新数据)
 * 时间范围：仅处理当前月份的数据
 * 执行频率：按需执行
 *
 * 修改时间: 2025-07-18 18:30
 */

// ===========================================
// 配置区
// ===========================================

const CONFIG = {
  // PaaS API配置
  PAAS_API_URL: "https://4689cn93cl12.vicp.fun/api/zijiezhen/manage",
  PAAS_API_KEY: "5a2f4fda3ab24bd0b10067ac701fecfd",

  // PaaS表名配置
  PAAS_BRAND_ARCHIVE: "tb___f_e624233ca743e567",
  PAAS_BRAND_TARGET: "tb___f_e624233ca743e567_197f862cf351062f",

  // WPS表名配置
  WPS_SOURCE_TABLE: "团队月度目标设定",
  WPS_LOG_TABLE: "脚本执行日志",

  // 字段映射配置
  FIELD_MAPPING: {
    // WPS字段名 → PaaS字段ID
    团队名称: "f_17f9ad55c1b69784",
    "初始目标（团队统筹）": "f_198172b5df846222",
    "调整配额（公司统筹）": "f_19812653e90a4a21",
    最终目标: "f_19812654fa92ae27",
  },

  // 数据验证配置
  VALIDATION: {
    MAX_INITIAL_TARGET_RATIO: 1.0, // 初始目标不得超过基础配额的100%
    MIN_TARGET_VALUE: 0, // 目标值最小值
  },
};

// 全局变量
let logBuffer = [];
const scriptStartTime = new Date();

// ===========================================
// 辅助函数
// ===========================================

/**
 * 获取当前月份
 *
 * 概述: 获取当前月份的YYYY-MM格式字符串
 * 调用的函数: JavaScript内置Date对象方法
 * 返回值: string - 当前月份字符串
 * 修改时间: 2025-07-18 18:30
 */
function getCurrentMonth() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  return `${year}-${month}`;
}

/**
 * 格式化日期时间
 *
 * 概述: 将Date对象格式化为可读字符串
 * 调用的函数: JavaScript内置Date对象方法
 * 参数: date (Date) - 日期对象
 * 返回值: string - 格式化的日期时间字符串
 * 修改时间: 2025-07-18 18:30
 */
function formatDateTime(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 辅助函数：将Date对象格式化为 "YYYY/MM/DD HH:MM:SS" 字符串 (WPS DateTime 格式)
 *
 * 概述: WPS表格专用的日期时间格式化函数
 * 调用的函数: JavaScript内置Date对象方法
 * 参数: date (Date) - 要格式化的Date对象
 * 返回值: string - 格式化后的日期时间字符串
 * 修改时间: 2025-07-18 18:30
 */
function formatDateTimeForWpsTable(date) {
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    date = new Date(); // 如果日期无效，则回退到当前时间
  }
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从0开始
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * HTTP请求封装
 *
 * 概述: 向PaaS系统发送manage接口更新请求
 * 调用的函数: WPS AirScript内置fetch函数
 * 参数: requestData (object) - 请求数据对象
 * 返回值: object - 包含成功标志和响应的结果对象
 * 修改时间: 2025-07-18 18:30
 */
function sendPaaSUpdate(requestData) {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-API-Key": CONFIG.PAAS_API_KEY,
    },
    body: JSON.stringify(requestData),
  };

  try {
    logBuffer.push(
      `[DEBUG] 向PaaS发送更新请求: ${JSON.stringify(requestData).substring(
        0,
        200
      )}...`
    );

    const response = fetch(CONFIG.PAAS_API_URL, requestOptions);

    if (response.status === 200) {
      const result = JSON.parse(response.body);
      return {
        success: true,
        data: result,
      };
    } else {
      return {
        success: false,
        error: `HTTP ${response.status}: ${response.body}`,
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `请求异常: ${error.message || error}`,
    };
  }
}

/**
 * 标准日志记录函数：写入AirScript执行日志到WPS表格
 *
 * 概述: 将AirScript的执行日志写入指定的WPS多维数据表（标准版）
 * 详细描述:
 *   1. 检查指定的日志表是否存在
 *   2. 如果日志表不存在，则创建包含两个固定字段的新表
 *   3. 如果日志表存在，则检查是否包含必需的两个字段
 *   4. 如果有必需的字段缺失，则尝试在日志表中创建这些字段
 *   5. 将传入的日志缓冲内容和脚本执行开始时间写入日志表的新记录中
 * 调用的函数:
 *   - Application.Sheet.GetSheets
 *   - Application.Sheet.CreateSheet
 *   - Application.Field.GetFields
 *   - Application.Field.CreateFields
 *   - Application.Record.CreateRecords
 *   - formatDateTimeForWpsTable
 * 参数: config (object) - 日志写入配置对象
 * 返回值: object - 包含执行状态的对象
 * 修改时间: 2025-07-18 18:30
 */
function writeAirScriptLogsToWpsTable(config) {
  // 参数解构
  const { logBuffer, logTableName, scriptStartTime } = config;

  const result = { success: false, logRecordId: null, error: null };

  if (
    typeof Application === "undefined" ||
    typeof Application.Sheet === "undefined"
  ) {
    result.error = "Application 或 Application.Sheet 未定义。可能非WPS环境。";
    console.error("[writeAirScriptLogsToWpsTable] " + result.error);
    if (logBuffer && logBuffer.length > 0) {
      console.error(
        "[writeAirScriptLogsToWpsTable] 缓存的日志:\\n" + logBuffer.join("\\n")
      );
    }
    return result;
  }

  if (!logBuffer || logBuffer.length === 0) {
    result.success = true; // 认为操作成功，因为没有日志需要写
    return result;
  }

  const logContentForTable = logBuffer.join("\\n");

  // 固定的字段定义
  const FIXED_LOG_FIELDS = [
    { name: "执行时间", type: "MultiLineText" },
    { name: "日志内容", type: "MultiLineText" },
  ];

  let logSheetId = null;
  try {
    const sheets = Application.Sheet.GetSheets();
    let existingLogSheet = null;
    if (Array.isArray(sheets)) {
      for (let i = 0; i < sheets.length; i++) {
        if (sheets[i] && String(sheets[i].name) === String(logTableName)) {
          existingLogSheet = sheets[i];
          break;
        }
      }
    }

    if (existingLogSheet) {
      logSheetId = Number(existingLogSheet.id);

      const existingFieldsResult = Application.Field.GetFields({
        SheetId: logSheetId,
      });
      const existingFieldNames = [];
      if (Array.isArray(existingFieldsResult)) {
        for (let i = 0; i < existingFieldsResult.length; i++) {
          if (existingFieldsResult[i] && existingFieldsResult[i].name != null) {
            existingFieldNames.push(String(existingFieldsResult[i].name));
          }
        }
      }

      const fieldsToAdd = [];
      for (let i = 0; i < FIXED_LOG_FIELDS.length; i++) {
        const requiredField = FIXED_LOG_FIELDS[i];
        if (
          !existingFieldNames.some(
            (name) => String(name) === String(requiredField.name)
          )
        ) {
          fieldsToAdd.push({
            name: String(requiredField.name),
            type: String(requiredField.type),
          });
        }
      }

      if (fieldsToAdd.length > 0) {
        try {
          const createFieldsResult = Application.Field.CreateFields({
            SheetId: logSheetId,
            Fields: fieldsToAdd,
          });
          if (
            !createFieldsResult ||
            !Array.isArray(createFieldsResult) ||
            createFieldsResult.length !== fieldsToAdd.length ||
            createFieldsResult.some((f) => !f || typeof f.id === "undefined")
          ) {
            console.error(
              `[错误][writeAirScriptLogsToWpsTable] 未能向 '${logTableName}' 添加部分/全部缺失字段. API响应: ${JSON.stringify(
                createFieldsResult
              )}`
            );
          }
        } catch (fieldCreationError) {
          console.error(
            `[错误][writeAirScriptLogsToWpsTable] 在为 '${logTableName}' 执行 Application.Field.CreateFields 时发生错误: ${
              fieldCreationError.message || JSON.stringify(fieldCreationError)
            }`
          );
        }
      }
    } else {
      try {
        const newSheet = Application.Sheet.CreateSheet({
          Name: String(logTableName),
          Views: [{ name: "所有日志", type: "Grid" }],
          Fields: FIXED_LOG_FIELDS,
        });
        if (newSheet && typeof newSheet.id !== "undefined") {
          logSheetId = Number(newSheet.id);
        } else {
          result.error = `创建日志表 '${logTableName}' 失败. API响应: ${JSON.stringify(
            newSheet
          )}`;
          console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
          return result;
        }
      } catch (sheetCreationError) {
        result.error = `在为 '${logTableName}' 执行 Application.Sheet.CreateSheet 时发生错误: ${
          sheetCreationError.message || JSON.stringify(sheetCreationError)
        }`;
        console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
        return result;
      }
    }

    if (logSheetId !== null) {
      const executionTimeFormatted = formatDateTimeForWpsTable(scriptStartTime);
      const recordDataFields = {
        执行时间: executionTimeFormatted,
        日志内容: logContentForTable,
      };

      try {
        const createRecordParams = {
          SheetId: logSheetId,
          Records: [{ fields: recordDataFields }],
        };
        const createResult =
          Application.Record.CreateRecords(createRecordParams);

        if (
          createResult &&
          Array.isArray(createResult) &&
          createResult.length > 0 &&
          typeof createResult[0].id !== "undefined"
        ) {
          result.success = true;
          result.logRecordId = createResult[0].id;
        } else {
          result.error = `未能将日志写入表 '${logTableName}'. API响应: ${JSON.stringify(
            createResult
          )}`;
          console.error(
            "[错误][writeAirScriptLogsToWpsTable] " +
              result.error +
              " 数据: " +
              JSON.stringify(recordDataFields)
          );
        }
      } catch (recordCreationError) {
        result.error = `在为 '${logTableName}' 执行 Application.Record.CreateRecords 时发生错误: ${
          recordCreationError.message || JSON.stringify(recordCreationError)
        }`;
        console.error(
          "[错误][writeAirScriptLogsToWpsTable] " +
            result.error +
            " 数据: " +
            JSON.stringify(recordDataFields)
        );
      }
    } else {
      result.error = "日志表的logSheetId为空，无法写入日志。";
      console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
    }
  } catch (e) {
    result.error = `在 writeAirScriptLogsToWpsTable 中发生意外错误: ${
      e.message || JSON.stringify(e)
    }`;
    console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
    if (e.stack)
      console.error(
        "[错误][writeAirScriptLogsToWpsTable] 错误堆栈: " + e.stack
      );
  }
  return result;
}

// ===========================================
// 主要函数
// ===========================================

/**
 * 从WPS获取团队目标设定数据
 *
 * 概述: 获取WPS表格中用户设定的团队目标数据
 * 调用的函数:
 *   - Application.Sheet.GetSheets
 *   - Application.Record.GetRecords
 * 参数: currentMonth (string) - 当前月份
 * 返回值: array - WPS团队目标数据数组
 * 修改时间: 2025-07-18 18:30
 */
function getWpsTargetData(currentMonth) {
  try {
    // 查找WPS源表格
    const sheets = Application.Sheet.GetSheets();
    let sourceSheet = null;

    for (let i = 0; i < sheets.length; i++) {
      if (String(sheets[i].name).trim() === CONFIG.WPS_SOURCE_TABLE) {
        sourceSheet = sheets[i];
        break;
      }
    }

    if (!sourceSheet) {
      throw new Error(`WPS源表格 ${CONFIG.WPS_SOURCE_TABLE} 不存在`);
    }

    // 获取表格记录
    const records = Application.Record.GetRecords({
      SheetId: Number(sourceSheet.id),
    });

    // 过滤当前月份的数据
    const filteredData = [];
    for (let i = 0; i < records.length; i++) {
      const record = records[i];
      const monthField = record.fields["月份"];

      if (monthField) {
        let recordMonth = "";
        if (monthField instanceof Date) {
          recordMonth = `${monthField.getFullYear()}-${String(
            monthField.getMonth() + 1
          ).padStart(2, "0")}`;
        } else if (typeof monthField === "string") {
          recordMonth = monthField.substring(0, 7); // 取YYYY-MM部分
        }

        if (recordMonth === currentMonth) {
          filteredData.push({
            recordId: record.id,
            团队名称: String(record.fields["团队名称"] || ""),
            基础配额: Number(record.fields["基础配额"] || 0),
            "初始目标（团队统筹）": Number(
              record.fields["初始目标（团队统筹）"] || 0
            ),
            "调整配额（公司统筹）": Number(
              record.fields["调整配额（公司统筹）"] || 0
            ),
            最终目标: Number(record.fields["最终目标"] || 0),
          });
        }
      }
    }

    logBuffer.push(
      `[INFO] 从WPS获取到 ${filteredData.length} 条 ${currentMonth} 的团队目标数据`
    );
    return filteredData;
  } catch (error) {
    logBuffer.push(`[ERROR] 获取WPS数据失败: ${error.message}`);
    throw error;
  }
}

/**
 * 数据验证函数
 *
 * 概述: 验证团队目标数据的合规性
 * 调用的函数: 无
 * 参数: wpsData (array) - WPS团队目标数据
 * 返回值: object - 包含验证结果和错误信息的对象
 * 修改时间: 2025-07-18 18:30
 */
function validateTeamTargetData(wpsData) {
  const validationErrors = [];
  const validRecords = [];

  for (let i = 0; i < wpsData.length; i++) {
    const record = wpsData[i];
    const errors = [];

    // 验证团队名称
    if (!record.团队名称 || record.团队名称.trim() === "") {
      errors.push("团队名称不能为空");
    }

    // 验证初始目标不超过基础配额
    if (record["初始目标（团队统筹）"] > record.基础配额) {
      errors.push(
        `初始目标(${record["初始目标（团队统筹）"]})不能超过基础配额(${record.基础配额})`
      );
    }

    // 验证目标值为非负
    if (
      record["初始目标（团队统筹）"] < CONFIG.VALIDATION.MIN_TARGET_VALUE ||
      record["调整配额（公司统筹）"] < CONFIG.VALIDATION.MIN_TARGET_VALUE
    ) {
      errors.push("目标值不能为负数");
    }

    // 验证最终目标计算正确性
    const calculatedFinalTarget =
      record["初始目标（团队统筹）"] + record["调整配额（公司统筹）"];
    if (Math.abs(record.最终目标 - calculatedFinalTarget) > 0.01) {
      errors.push(
        `最终目标(${record.最终目标})与计算值(${calculatedFinalTarget})不一致`
      );
    }

    if (errors.length > 0) {
      validationErrors.push({
        团队名称: record.团队名称,
        错误: errors,
      });
    } else {
      validRecords.push(record);
    }
  }

  return {
    isValid: validationErrors.length === 0,
    validRecords: validRecords,
    errors: validationErrors,
  };
}

/**
 * 构造PaaS更新数据
 *
 * 概述: 将WPS数据转换为PaaS系统可接受的更新格式
 * 调用的函数: getCurrentMonth
 * 参数: validRecords (array) - 验证通过的WPS记录
 * 返回值: object - PaaS更新请求数据
 * 修改时间: 2025-07-18 18:30
 */
function buildPaaSUpdateData(validRecords) {
  const currentMonth = getCurrentMonth();
  const updateData = {
    table: CONFIG.PAAS_BRAND_TARGET,
    action: "batch_update",
    condition_field: "f_197f8636118bbb37", // 生效月份字段
    condition_value: currentMonth,
    updates: [],
  };

  for (let i = 0; i < validRecords.length; i++) {
    const record = validRecords[i];

    // 构造更新记录
    const updateRecord = {
      condition: {
        f_197f8636118bbb37: currentMonth, // 生效月份
        // 需要通过团队名称找到对应的品牌ID，这里简化处理
        team_identifier: record.团队名称,
      },
      fields: {
        f_198172b5df846222: record["初始目标（团队统筹）"], // 初始目标
        f_19812653e90a4a21: record["调整配额（公司统筹）"], // 调整配额
        f_19812654fa92ae27: record.最终目标, // 最终目标
        f_1981266361e8ae5c: formatDateTime(new Date()), // 最后更新时间
      },
    };

    updateData.updates.push(updateRecord);
  }

  return updateData;
}

/**
 * 上传数据到PaaS系统
 *
 * 概述: 将构造好的数据上传到PaaS系统
 * 调用的函数: sendPaaSUpdate
 * 参数: updateData (object) - PaaS更新数据
 * 返回值: object - 上传结果
 * 修改时间: 2025-07-18 18:30
 */
function uploadToPaaS(updateData) {
  try {
    logBuffer.push(
      `[INFO] 开始上传 ${updateData.updates.length} 条记录到PaaS系统`
    );

    const result = sendPaaSUpdate(updateData);

    if (!result.success) {
      throw new Error(`PaaS数据上传失败: ${result.error}`);
    }

    logBuffer.push(`[INFO] 成功上传数据到PaaS系统`);
    return result.data;
  } catch (error) {
    logBuffer.push(`[ERROR] PaaS数据上传失败: ${error.message}`);
    throw error;
  }
}

/**
 * 生成执行报告
 *
 * 概述: 生成脚本执行的详细报告
 * 调用的函数: 无
 * 参数: processedData (object) - 处理过程数据
 * 修改时间: 2025-07-18 18:30
 */
function generateExecutionReport(processedData) {
  const { wpsDataCount, validationResult, uploadResult, processingTime } =
    processedData;

  logBuffer.push(`[REPORT] ========== 执行报告 ==========`);
  logBuffer.push(`[REPORT] WPS数据条数: ${wpsDataCount}`);
  logBuffer.push(`[REPORT] 验证通过: ${validationResult.validRecords.length}`);
  logBuffer.push(`[REPORT] 验证失败: ${validationResult.errors.length}`);

  if (validationResult.errors.length > 0) {
    logBuffer.push(`[REPORT] 验证错误详情:`);
    for (let i = 0; i < validationResult.errors.length; i++) {
      const error = validationResult.errors[i];
      logBuffer.push(
        `[REPORT]   - ${error.团队名称}: ${error.错误.join(", ")}`
      );
    }
  }

  if (uploadResult) {
    logBuffer.push(`[REPORT] PaaS上传: 成功`);
  }

  logBuffer.push(`[REPORT] 总处理时间: ${processingTime}ms`);
  logBuffer.push(`[REPORT] ==============================`);
}

/**
 * 主函数：执行WPS团队目标数据上传到PaaS
 *
 * 概述: 脚本的主要执行逻辑
 * 调用的函数:
 *   - getCurrentMonth
 *   - getWpsTargetData
 *   - validateTeamTargetData
 *   - buildPaaSUpdateData
 *   - uploadToPaaS
 *   - generateExecutionReport
 *   - writeAirScriptLogsToWpsTable
 * 修改时间: 2025-07-18 18:30
 */
function main() {
  const startTime = Date.now();

  try {
    logBuffer.push(
      `[INFO] ${formatDateTime(new Date())} 开始执行WPS团队目标数据上传`
    );

    // 1. 获取当前月份
    const currentMonth = getCurrentMonth();
    logBuffer.push(`[INFO] 当前处理月份: ${currentMonth}`);

    // 2. 从WPS获取数据
    logBuffer.push(`[INFO] 开始从WPS获取 ${currentMonth} 的团队目标数据`);
    const wpsData = getWpsTargetData(currentMonth);

    if (wpsData.length === 0) {
      logBuffer.push(
        `[WARN] WPS表格中没有 ${currentMonth} 的团队目标数据，无需上传`
      );
      return;
    }

    // 3. 数据验证
    logBuffer.push(`[INFO] 开始验证团队目标数据`);
    const validationResult = validateTeamTargetData(wpsData);

    if (!validationResult.isValid) {
      logBuffer.push(
        `[WARN] 数据验证发现 ${validationResult.errors.length} 个错误`
      );
      if (validationResult.validRecords.length === 0) {
        logBuffer.push(`[ERROR] 没有通过验证的记录，停止上传`);
        generateExecutionReport({
          wpsDataCount: wpsData.length,
          validationResult: validationResult,
          uploadResult: null,
          processingTime: Date.now() - startTime,
        });
        return;
      }
    }

    // 4. 构造PaaS更新数据
    logBuffer.push(`[INFO] 构造PaaS更新数据`);
    const updateData = buildPaaSUpdateData(validationResult.validRecords);

    // 5. 上传到PaaS
    logBuffer.push(`[INFO] 开始上传数据到PaaS系统`);
    const uploadResult = uploadToPaaS(updateData);

    // 6. 生成执行报告
    generateExecutionReport({
      wpsDataCount: wpsData.length,
      validationResult: validationResult,
      uploadResult: uploadResult,
      processingTime: Date.now() - startTime,
    });

    logBuffer.push(`[INFO] 脚本执行完成`);
  } catch (error) {
    logBuffer.push(`[ERROR] 脚本执行失败: ${error.message}`);
    console.error(`脚本执行失败: ${error.message}`);
  } finally {
    // 记录执行日志到WPS表格
    const loggingConfig = {
      logBuffer: logBuffer,
      logTableName: CONFIG.WPS_LOG_TABLE,
      scriptStartTime: scriptStartTime,
    };

    const loggingResult = writeAirScriptLogsToWpsTable(loggingConfig);

    if (!loggingResult.success) {
      console.error(
        "严重错误: 脚本完成时未能将执行日志写入WPS表: " + loggingResult.error
      );
    }
  }
}

// 执行主函数
main();
