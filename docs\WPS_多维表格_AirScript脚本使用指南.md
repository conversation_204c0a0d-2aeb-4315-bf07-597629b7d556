# 简介
多维表"开发"功能，给用户提供撰写 JavaScript 脚本的能力，使多维表能够执行自定义自动化任务。

# 说明
- 🔔更多内置功能请见： [https://airsheet.wps.cn/docs/api/build-in.html](https://airsheet.wps.cn/docs/api/build-in.html)
- 📌"开发" 功能已支持 HTTP 能力，详细调用请见：[https://airsheet.wps.cn/docs/api/advanced/HTTP.html](https://airsheet.wps.cn/docs/api/advanced/HTTP.html)
- 📢 脚本使用标准 JavaScript 创建。
    - version : ECMAScript6 , ECMAScript5
    - mode: Strict

# 自定义脚本规则
- **命名**：遵循 JavaScript 函数命名的标准规范，同时不能和OpenApi提供对象重名。
- **基本数据类型**：
    - Boolean type
    - Null type
    - Undefined type
    - Number type
    - String type
    - **API参数类型严格性**：WPS AirScript API 对接收的参数类型有严格要求。当使用API时，需要特别注意：
      - `SheetId` 必须是数字类型：从 `Application.Sheet.GetSheets()` 等API获取的 `sheet.id`，在使用它作为其他API（如 `Application.Field.GetFields()`）的 `SheetId` 参数时，请确保它确实是以数字类型传递的。如果曾将其转换为字符串，请在传递前用 `Number()` 转回数字。
      - 对象属性的类型必须匹配预期：API接收的对象（如字段、记录等）内部各属性值的类型必须符合预期，类型不匹配是导致API调用失败的常见原因。
    - **注意**：当从 `Application` 对象的 API (例如 `Application.Field.GetFields()`) 获取数据时，即使文档指明某个属性（如 `field.name`）为 `String` 类型，该值在脚本环境中的实际行为可能更接近一个特殊的对象。为了确保按预期进行操作，建议在使用这些值之前，通过 JavaScript 的内建函数（如 `String(value)`、`Number(value)`）将其显式转换为期望的原始类型。例如：
      ```javascript
      const fields = Application.Field.GetFields({ SheetId: yourSheetId });
      const fieldNames = fields.map(f => {
        if (f && f.name != null) {
          // 显式将 f.name 转换为原始字符串，然后再进行 trim
          return String(f.name).trim();
        }
        return null;
      }).filter(Boolean);
      // fieldNames 现在更可靠地包含了一个原始字符串的数组。
      ```
- **内置对象**：
    - Object
    - Array
    - Map
    - JSON
    - BigInt
    - Math
    - Date
    - Error 
- **内置函数**：
    - `isNaN()`
    - `parseFloat()`
    - `parseInt()`
    - `decodeURI()`
    - `decodeURIComponent()`
    - `encodeURI()`
    - `encodeURIComponent()`
    - `RegExp()`
- **全局对象**：
    - `Application` (DB OpenApi)
- **日志输出**： 
    - `console.log` 
    - `console.error`
    - `console.info`
    - **记录数组内容的建议**：当使用 `console.log` 记录包含从 `Application` API 获取并处理过的数据的数组时，直接使用 `array.join(', ')` 或 `JSON.stringify(array)` 可能导致输出与预期不符（例如，数组元素可能显示为 `[object Promise]`，即使它们理论上已经是字符串）。如果遇到这种情况，可以考虑手动遍历数组并构建一个字符串来记录数组内容，以获得更准确的日志输出。例如：
      ```javascript
      // 假设 anArray 是一个处理过的字段名数组
      let logOutput = "无内容";
      if (anArray.length > 0) {
        logOutput = "'" + String(anArray[0]) + "'";
        for (let i = 1; i < anArray.length; i++) {
          logOutput += ", '" + String(anArray[i]) + "'";
        }
      }
      console.log(`数组内容: [${logOutput}] (第一个元素类型: ${anArray.length > 0 ? typeof anArray[0] : 'N/A'})`);
      ```
    - **调试的重要性**：在与 AirScript API 交互或进行复杂数据转换时，**在代码的关键路径上大量使用 `console.log()` 是至关重要的调试手段**。建议记录的内容包括：变量的值、变量的类型 (`typeof variable`)、对象/数组的结构 (`JSON.stringify(variable)`)、API调用的输入参数和返回值等。通过细致的日志，可以逐步缩小问题范围，理解数据在哪个环节发生了非预期的变化。
- **循环与迭代注意事项**：
    - **`forEach` vs `for` 循环**：在 AirScript 环境中，当循环内部逻辑需要修改循环外部声明的变量，并期望这些修改立即对后续迭代或循环外的代码可见时，JavaScript 的 `forEach` 方法的行为可能与标准浏览器或 Node.js 环境略有差异。**优先考虑使用传统的 `for (var i = 0; i < array.length; i++)` 循环**，它提供了更明确和可预测的同步执行保证。
    - **案例**：使用 `forEach` 遍历数据库行并收集列名到外部数组时，外部数组可能未按预期填充；改用 `for` 循环后问题解决。
    ```javascript
    // 不推荐: 在AirScript环境中可能导致外部变量修改不可靠
    /*
    let allColumns = [];
    rows.forEach(function(row) {
      Object.keys(row).forEach(function(key) {
        if (!allColumns.includes(key)) allColumns.push(key);
      });
    });
    */
    
    // 推荐: 更可靠的同步执行
    let allColumns = [];
    for (let i = 0; i < rows.length; i++) {
      let row = rows[i];
      let keys = Object.keys(row);
      for (let j = 0; j < keys.length; j++) {
        let key = keys[j];
        if (!allColumns.includes(key)) allColumns.push(key);
      }
    }
    ```
- **自动补全**：
    支持自定义函数的自动补全功能。支持JsDoc。
    ```javascript
    /**
     * 数字增加1.
     *
     * @param {number} input 被加数.
     * @return +1.
     * @customfunction
     */
    function inc(input) {
      return input + 1;
    }
    ```

# 自动化中运行
👋新版本自动化请看这里  [https://kdocs.cn/l/cdQOqc6TZuMk](https://kdocs.cn/l/cdQOqc6TZuMk)

在自动化中，如果左侧是增改多维表数据，右侧是执行脚本，那么可传递 2 个变量：
- `Context.argv.id`：执行行的 ID
- `Context.argv.sheetId`：执行行的表 ID

如果希望调试这 2 个变量，可以将它发送到金山协作或者企业微信机器人：
```javascript
// 金山协作示例
HTTP.fetch(机器人地址, {
  method: "POST",
  body: JSON.stringify({
    "msgtype": "markdown",
    "markdown": {
      "text": `${Context.argv.sheetId} ${Context.argv.id}`
    }
  }),
});
```

# 快速上手
[https://kdocs.cn/l/ctx1jAV1xJhR](https://kdocs.cn/l/ctx1jAV1xJhR)

# 时区处理说明

## 1. 运行环境时区问题

⚠️ **重要提醒**：WPS AirScript的运行环境**不是默认中国时区**，而是使用UTC时区或其他时区。这意味着直接使用 `new Date()` 或其他JavaScript时间函数获取的时间可能不是北京时间。

### 问题表现
- 使用 `new Date()` 获取的时间与北京时间存在时差
- 多维表中时间字段的记录可能不是中国时区时间
- 日志记录和调试信息的时间显示与实际操作时间不符
- 与其他系统的时间同步出现偏差

### 影响范围
- 所有涉及时间记录的业务逻辑
- 多维表时间字段的创建和更新
- 日志记录和调试信息的时间显示
- 自动化流程中的时间判断和处理
- 与外部系统的时间同步

## 2. 中国时区解决方案

为了确保获取准确的中国时区时间，我们需要实现专门的时区处理函数：

### 2.1 时区处理辅助函数

```javascript
/**
 * 获取中国时区（UTC+8）的当前时间
 * @returns {Date} 中国时区的当前时间对象
 */
function getChinaTime() {
    const now = new Date();
    // 获取UTC时间戳
    const utcTime = now.getTime() + (now.getTimezoneOffset() * 60000);
    // 添加8小时（中国时区UTC+8）
    const chinaTime = new Date(utcTime + (8 * 3600000));
    return chinaTime;
}

/**
 * 获取中国时区（UTC+8）的当前时间字符串
 * @param {string} format - 时间格式，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化的中国时区时间字符串
 */
function getChinaTimeStr(format = 'YYYY-MM-DD HH:mm:ss') {
    const chinaTime = getChinaTime();
    
    const year = chinaTime.getFullYear();
    const month = String(chinaTime.getMonth() + 1).padStart(2, '0');
    const day = String(chinaTime.getDate()).padStart(2, '0');
    const hours = String(chinaTime.getHours()).padStart(2, '0');
    const minutes = String(chinaTime.getMinutes()).padStart(2, '0');
    const seconds = String(chinaTime.getSeconds()).padStart(2, '0');
    
    if (format === 'YYYY/MM/DD') {
        return `${year}/${month}/${day}`;
    } else if (format === 'HH:mm:ss') {
        return `${hours}:${minutes}:${seconds}`;
    } else {
        // 默认格式 'YYYY-MM-DD HH:mm:ss'
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
}

/**
 * 获取中国时区的日期字符串（用于WPS多维表日期字段）
 * @returns {string} 格式为 'YYYY/MM/DD' 的日期字符串
 */
function getChinaDateStr() {
    return getChinaTimeStr('YYYY/MM/DD');
}

/**
 * 获取中国时区的时间字符串（用于WPS多维表时间字段）
 * @returns {string} 格式为 'HH:mm:ss' 的时间字符串
 */
function getChinaTimeOnlyStr() {
    return getChinaTimeStr('HH:mm:ss');
}
```

### 2.2 兼容性设计

上述解决方案采用了兼容性设计：

1. **通用时区转换**：通过UTC时间戳计算，确保在任何时区环境下都能正确获取中国时区时间
2. **多种格式支持**：提供不同格式的时间字符串，适配WPS多维表的不同字段类型
3. **简单易用**：函数接口简洁，便于在各种场景中使用

### 2.3 使用示例

```javascript
// 错误的做法（可能不是中国时区时间）
// const currentTime = new Date().toISOString();

// 正确的做法（确保是中国时区时间）
const currentTime = getChinaTimeStr();
console.log(`当前中国时区时间: ${currentTime}`);

// 用于多维表日期字段
const dateValue = getChinaDateStr(); // "2025/01/27"

// 用于多维表时间字段
const timeValue = getChinaTimeOnlyStr(); // "18:30:15"

// 用于多维表文本字段（完整时间戳）
const timestampValue = getChinaTimeStr(); // "2025-01-27 18:30:15"

// 创建记录时使用中国时区时间
const records = Application.Record.CreateRecords({
    SheetId: sheetId,
    Records: [{
        fields: {
            '操作时间': getChinaDateStr(),
            '具体时刻': getChinaTimeOnlyStr(),
            '完整时间戳': getChinaTimeStr(),
            '操作内容': '执行了某项业务操作'
        }
    }]
});

// 日志记录使用中国时区时间
console.log(`[${getChinaTimeStr()}] 开始执行脚本`);
```

## 3. 最佳实践建议

### 3.1 统一时间获取
- 在脚本开头定义时区处理函数
- 所有需要获取当前时间的地方都使用 `getChinaTimeStr()` 系列函数
- 避免直接使用 `new Date()` 或 `Date.now()`

### 3.2 时间格式标准化
- 日期字段使用 `YYYY/MM/DD` 格式（WPS多维表日期字段标准格式）
- 时间字段使用 `HH:mm:ss` 格式（WPS多维表时间字段标准格式）
- 文本字段中的完整时间戳使用 `YYYY-MM-DD HH:mm:ss` 格式
- 便于日志查看和问题排查

### 3.3 代码示例模板
```javascript
// 时区处理函数（放在脚本开头）
function getChinaTime() {
    const now = new Date();
    const utcTime = now.getTime() + (now.getTimezoneOffset() * 60000);
    const chinaTime = new Date(utcTime + (8 * 3600000));
    return chinaTime;
}

function getChinaTimeStr(format = 'YYYY-MM-DD HH:mm:ss') {
    const chinaTime = getChinaTime();
    const year = chinaTime.getFullYear();
    const month = String(chinaTime.getMonth() + 1).padStart(2, '0');
    const day = String(chinaTime.getDate()).padStart(2, '0');
    const hours = String(chinaTime.getHours()).padStart(2, '0');
    const minutes = String(chinaTime.getMinutes()).padStart(2, '0');
    const seconds = String(chinaTime.getSeconds()).padStart(2, '0');
    
    if (format === 'YYYY/MM/DD') {
        return `${year}/${month}/${day}`;
    } else if (format === 'HH:mm:ss') {
        return `${hours}:${minutes}:${seconds}`;
    } else {
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
}

// 主要业务逻辑
function main() {
    // 记录脚本开始时间（中国时区）
    const scriptStartTime = getChinaTimeStr();
    console.log(`脚本开始时间: ${scriptStartTime}`);
    
    // 获取当前视图
    const currentView = Application.Selection.GetActiveView();
    console.log(`当前视图: ${currentView.name} (${getChinaTimeStr()})`);
    
    // 创建带时间戳的记录
    const records = Application.Record.CreateRecords({
        SheetId: currentView.sheetId,
        Records: [{
            fields: {
                '操作时间': getChinaTimeStr('YYYY/MM/DD'),
                '执行时刻': getChinaTimeStr('HH:mm:ss'),
                '完整时间戳': getChinaTimeStr(),
                '操作类型': '脚本自动执行'
            }
        }]
    });
    
    // 记录脚本结束时间（中国时区）
    console.log(`脚本结束时间: ${getChinaTimeStr()}`);
}

// 执行主函数
main();
```

## 4. 注意事项

### 4.1 时区一致性
- 确保整个项目中的时间处理保持一致
- 与数据库、Python脚本、前端系统的时区设置保持同步
- 在团队开发中统一时区处理标准

### 4.2 WPS多维表字段类型适配
- **日期字段 (Date)**：使用 `getChinaTimeStr('YYYY/MM/DD')` 格式
- **时间字段 (Time)**：使用 `getChinaTimeStr('HH:mm:ss')` 格式
- **文本字段 (MultiLineText)**：可使用完整的 `getChinaTimeStr()` 格式
- **自动时间字段**：如 `CreatedTime` 等自动字段由系统管理，无需手动设置

### 4.3 测试验证
- 在不同环境中测试时区处理的正确性
- 验证生成的时间戳是否为北京时间
- 检查多维表中存储的时间是否正确
- 与其他系统的时间同步进行验证

### 4.4 调试和日志
- 在关键操作前后记录中国时区时间
- 使用 `console.log` 输出时间信息便于调试
- 建立时间相关的错误处理机制

### 4.5 自动化流程集成
- 在自动化流程中传递时间参数时，确保使用中国时区时间
- 与Python脚本配合时，保持时区处理的一致性
- 考虑夏令时等特殊情况（中国不使用夏令时，UTC+8固定）

# 附件1：多维表 API 说明

## Sheet 表操作

### 1. 获取所有表信息
- **调用方式**：`Application.Sheet.GetSheets()`
- **返回值**：返回 `Array[]` 表示获取的多张表数据，每条数据记录单项表的各种信息，单条数据其字段说明如下所示

| 属性           | 数据类型 | 说明         |
| -------------- | -------- | ------------ |
| `fields`       | `Array[]`| 表字段信息    |
| `id`           | `Number` | 表Id         |
| `name`         | `String` | 表名称       |
| `primaryFieldId`| `String` | 表首字段Id   |
| `recordsCount` | `Number` | 表的记录数量 |
| `views`        | `Array[]`| 表的视图列表 |

**开发提示：迭代 `GetSheets()` 返回结果**

`Application.Sheet.GetSheets()` 返回一个包含所有工作表对象的数组（或行为类似数组的对象）。虽然 `Array.isArray()` 对其返回 `true` 且 `length` 属性正确，但在某些情况下，直接使用 `forEach` 方法遍历可能无法按预期工作（例如，如果返回的是稀疏数组或其在 AirScript 环境中的迭代器行为与 `forEach` 不兼容）。

为了确保稳定可靠地遍历返回的工作表对象，建议使用传统的 `for` 循环：
```javascript
const wps_sheets_result = Application.Sheet.GetSheets();
if (Array.isArray(wps_sheets_result)) {
  for (let i = 0; i < wps_sheets_result.length; i++) {
    const sheet = wps_sheets_result[i];
    // 确保 sheet 对象实际存在且有效
    if (sheet && typeof sheet.name !== 'undefined' && typeof sheet.id !== 'undefined') { 
      // 在这里处理 sheet 对象，例如：
      // console.log(`Sheet Name: ${String(sheet.name)}, ID: ${String(sheet.id)}`);
      // console.log(`Sheet Object at index ${i}: ${JSON.stringify(sheet)}`);
    } else {
      // console.log(`Sheet at index ${i} is null, undefined, or lacks essential properties.`);
    }
  }
}
```
这种基于索引的迭代方式对于处理 `Application.Sheet.GetSheets()` 可能返回的各种数组状结构更为健壮。同时，请注意检查数组中的每个元素（`sheet` 对象）是否确实有效，以及其属性（如 `name`, `id`）是否存在，然后再使用它们。

- **代码示例**:
    ```javascript
    const sheets = Application.Sheet.GetSheets()
    console.log(sheets)
    ```
- **返回数据示例**:
    ```json
    [
      {"fields":[
         {"id":"F","name":"名称","type":"MultiLineText"},
         {"id":"G","name":"分类","type":"MultiLineText"},
         {"id":"S","name":"数量","type":"Number"},
         {"id":"q","name":"联系人","supportMulti":true,"type":"Contact"}
        ],
        "id":3,
        "name":"商品表",
        "primaryFieldId":"F",
        "recordsCount":304,
        "views":[
           {"id":"C","name":"表","recordsCount":0,"type":"Grid"},
           {"id":"I","name":"表视图","recordsCount":304,"type":"Grid"},
           {"id":"J","name":"看板视图","recordsCount":304,"type":"Kanban"},
         ]
       },
       {"fields":[
           {"id":"H","name":"名称","type":"MultiLineText"},
           {"displayText":"","id":"T","name":"跳转","type":"Url"},
           {"id":"W","items":[{"color":4282238394,"id":"T","value":"look"},
           {"color":4282238394,"id":"U","value":"money"},
           {"color":4282238394,"id":"V","value":"body"}
         ],
        "id":4,
        "name":"商品表1",
        "primaryFieldId":"G",
        "recordsCount":304,
         "views":[
           {"id":"C","name":"表","recordsCount":0,"type":"Grid"},
         ]
       }
     ]
    ```

### 2. 创建新的表
- **调用方式**：`Application.Sheet.CreateSheet()`
- **请求参数**：

| 属性   | 数据类型 | 默认值 | 必填 | 说明                                                                                                                               |
| ------ | -------- | ------ | ---- | ---------------------------------------------------------------------------------------------------------------------------------- |
| `Name` | `String` | -      | 是   | 表名称                                                                                                                             |
| `Views`| `Array[]`| -      | 是   | - 表视图信息 <br> - 视图类型说明见  附件3                                                                                               |
| `Fields`| `Array[]`| -      | 是   | - 最少传入一个字段 <br> - 表示表字段信息 <br> - 每种字段（Field) 创建方式详见附件2 <br> **举例请见下方说明** |

**`Fields` 参数举例**:
```javascript
[
    { name: '名称', type: 'MultiLineText' }, 
    { name: "分类", 
      type: "SingleSelect", 
      items: [{value: "A"},{value: "B"}, {value: "C" }] 
    }
] 
```

- **返回值**：返回 `Object` 表示新创建表信息的集合，`Object`内字段说明如下所示

| 属性           | 数据类型 | 说明           |
| -------------- | -------- | -------------- |
| `fields`       | `Array[]`| 表字段信息     |
| `id`           | `Number` | 表Id           |
| `name`         | `String` | 表名称         |
| `primaryFieldId`| `String` | 表首字段Id     |
| `recordsCount` | `Number` | 表的记录数量   |
| `views`        | `Array[]`| 表下的视图列表 |

- **代码示例**:
    ```javascript
    const sheet = Application.Sheet.CreateSheet({ 
        Name: '商品表',  
        Views: [{ name: '表', type: 'Grid' }], 
        Fields: [
            { name: '名称', type: 'MultiLineText' }, 
            { name: "分类", 
              type: "SingleSelect", 
              items: [{value: "A"},{value: "B"}, {value: "C" }] 
            }
        ] 
    })
    ```
- **返回数据示例**:
    ```json
    {
      "fields":[
        {"id":"LX","name":"名称","type":"MultiLineText"},
        {"id":"LY","items":[{"color":4282238394,"id":"DZ","value":"A"},
                             {"color":4282238394,"id":"Da","value":"B"},
                             {"color":4282238394,"id":"Db","value":"C"}
                            ],"name":"分类","type":"SingleSelect"}
      ],
      "id":52,
      "name":"商品表",
      "primaryFieldId":"LX",
      "recordsCount":1,
      "views":[
        {"id":"Cr",
         "name":"表",
         "recordsCount":1,
         "type":"Grid"
         }
      ]
    }
    ```

### 3. 删除表
- **调用方式**：`Application.Sheet.DeleteSheet()`
- **请求参数**：

| 属性    | 数据类型 | 默认值 | 必填 | 说明 |
| ------- | -------- | ------ | ---- | ---- |
| `SheetId`| `Number` | -      | 是   | 表Id |

- **返回值**：返回 `Object` ，`Object`内含有字段说明如下所示

| 属性 | 数据类型 | 说明 |
| ---- | -------- | ---- |
| `id` | `Number` | 表Id |

- **代码示例**:
    ```javascript
    Application.Sheet.DeleteSheet({ SheetId: 8 }) 
    ```
- **返回数据示例**:
    ```json
    {"id":8}
    ```

### 4. 更新表名字
- **调用方式**：`Application.Sheet.UpdateSheet()`
- **请求参数**：

| 属性    | 数据类型 | 默认值 | 必填 | 说明         |
| ------- | -------- | ------ | ---- | ------------ |
| `SheetId`| `Number` | -      | 是   | 表Id         |
| `Name`   | `String` | -      | 是   | 更新的表名称 |

- **返回值**：返回 `bool` ，"true"表示更新成功，"false"表示更新失败
- **代码示例**:
    ```javascript
    const result = Application.Sheet.UpdateSheet({ SheetId: 9, Name: '备份表' })
    ```
- **返回数据示例**:
    ```json
    true
    ```

## View 视图操作

### 1. 获取视图信息
- **调用方式**：`Application.View.GetViews()`
- **请求参数**：

| 属性    | 数据类型 | 默认值 | 必填 | 说明 |
| ------- | -------- | ------ | ---- | ---- |
| `SheetId`| `Number` | -      | 是   | 表Id |

- **返回值**：返回 `Array[]` ，表示获取的表下所有视图的列表，单项视图信息中的字段说明如下所示

| 属性         | 数据类型 | 说明                                   |
| ------------ | -------- | -------------------------------------- |
| `id`         | `String` | 视图Id                                 |
| `name`       | `String` | 视图名称                               |
| `recordsCount`| `Number` | 视图的记录数量                         |
| `type`       | `String` | 视图类型 <br> 视图类型说明详见 附件3 |

- **代码示例**:
    ```javascript
    const views = Application.View.GetViews({ SheetId: 1 })
    ```
- **返回数据示例**:
    ```json
    [
        {"id":"Cm","name":"表","recordsCount":1,"type":"Grid"},
        {"id":"Cn","name":"看板","recordsCount":1,"type":"Kanban"},
        {"id":"Co","name":"画册","recordsCount":1,"type":"Gallery"},
        {"id":"Cp","name":"表单","recordsCount":1,"type":"Form"},
        {"id":"Cq","name":"甘特","recordsCount":1,"type":"Gantt"}
    ]
    ```

### 2. 创建视图
- **调用方式**：`Application.View.CreateView()`
- **请求参数**：

| 属性     | 数据类型 | 默认值 | 必填 | 说明                                   |
| -------- | -------- | ------ | ---- | -------------------------------------- |
| `SheetId`  | `Number` | -      | 是   | 表Id                                   |
| `Name`     | `String` | -      | 是   | 视图名称                               |
| `ViewType` | `String` | -      | 是   | 视图类型 <br> 视图类型说明详见 附件3 |

- **返回值**：返回 `Object`，内含新创建视图的各种信息，视图信息中的字段说明如下所示

| 属性         | 数据类型 | 说明                                   |
| ------------ | -------- | -------------------------------------- |
| `id`         | `String` | 视图Id                                 |
| `name`       | `String` | 视图名称                               |
| `recordsCount`| `Number` | 视图的记录数量                         |
| `type`       | `String` | 视图类型 <br> 视图类型说明详见 附件3 |

- **代码示例**:
    ```javascript
    const view = Application.View.CreateView({ 
        SheetId: 1, 
        Name: '看板', 
        ViewType: 'Kanban' 
    })
    ```
- **返回数据示例**:
    ```json
    {"id":"Cu","name":"看板","recordsCount":18,"type":"Kanban"}
    ```

### 3. 删除视图
- **调用方式**：`Application.View.DeleteView()`
- **请求参数**：

| 属性    | 数据类型 | 默认值 | 必填 | 说明   |
| ------- | -------- | ------ | ---- | ------ |
| `SheetId`| `Number` | -      | 是   | 表Id   |
| `ViewId` | `String` | -      | 是   | 视图Id |

- **返回值**：返回 `Object` ，表示已删除的视图Id

| 属性 | 数据类型 | 说明   |
| ---- | -------- | ------ |
| `id` | `String` | 视图Id |

- **代码示例**:
    ```javascript
    Application.View.DeleteView({ SheetId: 1, ViewId: 'F' })
    ```
- **返回数据示例**:
    ```json
    {"id":"Cw"}
    ```

### 4. 修改视图名称
- **调用方式**：`Application.View.UpdateView()`
- **请求参数**：

| 属性    | 数据类型 | 默认值 | 必填 | 说明           |
| ------- | -------- | ------ | ---- | -------------- |
| `SheetId`| `Number` | -      | 是   | 表Id           |
| `ViewId` | `String` | -      | 是   | 视图Id         |
| `Name`   | `String` | -      | 是   | 更新的视图名称 |

- **返回值**：返回`Object` ，表示已更新的视图信息，视图信息中的字段说明如下所示

| 属性         | 数据类型 | 说明                                   |
| ------------ | -------- | -------------------------------------- |
| `id`         | `String` | 视图Id                                 |
| `name`       | `String` | 视图名称                               |
| `recordsCount`| `Number` | 视图的记录数量                         |
| `type`       | `String` | 视图类型 <br> 视图类型说明详见 附件3 |

- **代码示例**:
    ```javascript
    Application.View.UpdateView({ 
        SheetId: 8, 
        ViewId: 'E', 
        Name: '名单' 
    })
    ```
- **返回数据示例**:
    ```json
    {"id":"E","name":"名单","recordsCount":1,"type":"Grid"}
    ```

## Field 字段操作

### 1. 获取字段信息
- **调用方式**：`Application.Field.GetFields()`
- **请求参数**：

| 属性    | 数据类型 | 默认值 | 必填 | 说明 |
| ------- | -------- | ------ | ---- | ---- |
| `SheetId`| `Number` | -      | 是   | 表Id |

- **返回值**：返回`Array[]` ，表示获取的表所有字段信息，字段基本属性说明如下所示

| 属性 | 数据类型 | 说明                                   |
| ---- | -------- | -------------------------------------- |
| `id` | `String` | 字段Id                                 |
| `name`| `String` | 字段名称                               |
| `type`| `String` | 字段类型 <br> 具体字段类型说明详见附件2 |

- **代码示例**:
    ```javascript
    const fields = Application.Field.GetFields({ SheetId: 1 })
    ```
- **返回数据示例**:
    ```json
    [
      {"id":"Ce","name":"名称","type":"MultiLineText"},
      {"id":"Cf","name":"数量","type":"Number"},
      {"id":"Cg","name":"日期","type":"Date"},
      {"id":"Ch","name":"时间","type":"Time"},
      {"id":"Ci","name":"复选框","type":"Checkbox"},
      {"displayText":"","id":"Cj","name":"超链接","type":"Url"}
    ] 
    ```

### 2. 创建字段
- **调用方式**：`Application.Field.CreateFields()`
- **请求参数**：

| 属性    | 数据类型 | 默认值 | 必填 | 说明                                       |
| ------- | -------- | ------ | ---- | ------------------------------------------ |
| `SheetId`| `Number` | -      | 是   | 表Id                                       |
| `Fields` | `Array[]`| -      | 是   | 表的字段信息 <br> 字段(Field) 格式说明见  附件2 |

- **返回值**：返回`Array[]`，表示已创建的表所有字段信息，字段基本属性说明如下所示

| 属性 | 数据类型 | 说明                                   |
| ---- | -------- | -------------------------------------- |
| `id` | `String` | 字段Id                                 |
| `name`| `String` | 字段名称                               |
| `type`| `String` | 字段类型 <br> 具体字段类型说明详见附件2 |

- **代码示例**:
    ```javascript
    const field =  Application.Field.CreateFields({ 
        SheetId: 3, 
        Fields: [ 
            { name: '等级',  type: 'Rating', max: 5 }
        ] 
    })
    ```
- **返回数据示例**:
    ```json
    [{"id":"LZ","name":"等级","type":"Rating"}]
    ```

**重要实践：字段对象的构造方式**

在使用 `Application.Field.CreateFields()` 创建字段等复杂对象时，为避免API参数对象的属性在传递过程中出现"丢失"情况，**强烈建议采用传统、明确的方式构建对象**：

```javascript
// 不推荐 (可能导致属性丢失问题)
/*
var new_fields = fields_to_add.map(function(fieldName) {
  var trimmedName = String(fieldName).trim();
  if (trimmedName) {
    return { name: trimmedName, type: "MultiLineText" }; // 此处返回的对象，其name属性可能在后续丢失
  }
  return null;
}).filter(Boolean);
*/

// 推荐 (更稳健的对象构造方式)
var new_fields_for_api = [];
for (var i = 0; i < fields_to_add.length; i++) {
  var fieldName_str = String(fields_to_add[i]).trim();
  if (fieldName_str) {
    var field_object = {};
    field_object.name = fieldName_str;
    field_object.type = "MultiLineText"; // 假设默认为MultiLineText
    new_fields_for_api.push(field_object);
  }
}
// Application.Field.CreateFields({ SheetId: sheetId, Fields: new_fields_for_api }); 
```

这种方法比链式调用 `.map(item => ({ name: item.name, ... })).filter(...)` 更能确保对象属性在传递给API时的完整性和稳定性。实践证明，即使返回的对象字面量看起来正确，在API调用时仍可能报错，如 `Fields[0].name` 为 `undefined`。

### 3. 删除字段
- **调用方式**：`Application.Field.DeleteFields()`
- **请求参数**：

| 属性     | 数据类型 | 默认值 | 必填 | 说明             |
| -------- | -------- | ------ | ---- | ---------------- |
| `SheetId`  | `Number` | -      | 是   | 表Id             |
| `FieldIds` | `Array[]`| -      | 是   | 需要删除的字段Id |

- **返回值**：返回`Array[]`，内含删除的表id以及删除是否成功，字段基本属性说明如下所示

| 属性    | 数据类型 | 说明                                           |
| ------- | -------- | ---------------------------------------------- |
| `id`    | `String` | 字段Id                                         |
| `deleted`| `Bool`   | 是否删除成功 <br> "true"表示删除成功，"false"表示删除失败 |

- **代码示例**:
    ```javascript
    const resutlt = Application.Field.DeleteFields({ SheetId: 8, FieldIds: ['P', 'Q'] })
    ```
- **返回数据示例**:
    ```json
    [{"deleted":false,"id":"P"},{"deleted":false,"id":"Q"}]
    ```

### 4. 更新字段
- **调用方式**：`Application.Field.UpdateFields()`
- **请求参数**：

| 属性    | 数据类型 | 默认值 | 必填 | 说明                                                               |
| ------- | -------- | ------ | ---- | ------------------------------------------------------------------ |
| `SheetId`| `Number` | -      | 是   | 表Id                                                               |
| `Fields` | `Array[]`| -      | 是   | 更新的字段信息，包含字段Id，字段name等等 <br> 字段(Field) 格式说明见  附件2 |

- **返回值**：返回`Array[]`，表示已更新的字段信息，字段基本属性说明如下所示

| 属性 | 数据类型 | 说明                                   |
| ---- | -------- | -------------------------------------- |
| `id` | `String` | 字段Id                                 |
| `name`| `String` | 字段名称                               |
| `type`| `String` | 字段类型 <br> 具体字段类型说明详见  附件2 |

- **代码示例**:
    ```javascript
    Application.Field.UpdateFields({ 
        SheetId: 3, 
        Fields: [{ id: 'LG', name: '跳转' }]
    })
    ```
- **返回数据示例**:
    ```json
    [
      {"displayText":"","id":"LG","name":"跳转","type":"Url"}
    ]
    ```

## Record 行记录

### 1. 获取行记录（多条）
🔔 **注意**：每次请求最多返回100条，数据量大的时候请使用分页查询
- **调用方式**：`Application.Record.GetRecords()`
- **请求参数**：

| 属性       | 数据类型 | 默认值 | 必填 | 说明                                                                                                                            |
| ---------- | -------- | ------ | ---- | ------------------------------------------------------------------------------------------------------------------------------- |
| `SheetId`  | `Number` | -      | 是   | 表Id                                                                                                                            |
| `ViewId`   | `String` | -      | 否   | 填写后将从被指定的视图获取该用户所见到的记录；若不填写，则从工作表获取记录                                                              |
| `PageSize` | `Number` | 100    | 否   | 存在分页时，指定本次查询的起始记录（含）。若不填写或填写为空字符串，则从第一条记录开始获取 <br> 当前最大值：1000                           |
| `Offset`   | `Number` | -      | 否   | 分页查询时，将返回一个offset值，指向下一页的第一条记录，供后续查询。查询到最后一页或第maxRecords条记录时，返回数据将不再包含offset值        |
| `MaxRecords`| `Number` | -      | 否   | 指定要获取的"前maxRecords条记录"，若不填写，则默认返回全部记录                                                                        |
| `Fields`   | `Array[]`| -      | 否   | 具体字段类型说明详见  附件2                                                                                                       |
| `Filter`   | `Object` | -      | 否   | 详细说明见 附件4                                                                                                                |

- **返回值**：返回`Array[]`，表示获取表的所有记录，每条记录包含基本信息说明如下

| 属性   | 数据类型 | 说明                                                                 |
| ------ | -------- | -------------------------------------------------------------------- |
| `id`   | `String` | 记录Id                                                               |
| `Fields`| `Object` | 获取的字段信息，包含字段Id，字段name等等 <br> 字段(Field) 格式说明见  附件2 |

- **代码示例**:
    ```javascript
    const records = Application.Record.GetRecords({ SheetId: 3 })
    console.log(records.records)
    ```
- **返回数据示例**:
    ```json
    [
        {"fields":{"日期":"2023/02/21"},"id":"BH5"},
        {"fields":{"日期":"2023/02/08"},"id":"BIs"}
    ]
    ```
- **分页查询例子**:
    ```javascript
    function fetchAllRecords() {
      const view = Application.Selection.GetActiveView()
      let all = []
      let offset = null;

      while (all.length === 0 || offset) {
        let records = Application.Record.GetRecords({
          SheetId: view.sheetId,
          ViewId: view.viewId,
          Offset: offset,
        })
        offset = records.offset
        all = all.concat(records.records)
      }
      console.log(all.length)
      return all
    }

    fetchAllRecords()
    ```

### 2. 获取行记录（单条）
- **调用方式**：`Application.Record.GetRecord()`
- **请求参数**：

| 属性     | 数据类型 | 默认值 | 必填 | 说明                     |
| -------- | -------- | ------ | ---- | ------------------------ |
| `SheetId`  | `Number` | -      | 是   | 表Id                     |
| `RecordId` | `String` | -      | 是   | 表中指定获取的记录id |

- **返回值**：返回`Object`，表示获取表的指定的单条记录，记录包含基本信息说明如下

| 属性   | 数据类型 | 说明                                                                 |
| ------ | -------- | -------------------------------------------------------------------- |
| `id`   | `String` | 记录Id                                                               |
| `Fields`| `Object` | 获取的字段信息，包含字段Id，字段name等等 <br> 字段(Field) 格式说明见  附件2 |

- **代码示例**:
    ```javascript
    const record = Application.Record.GetRecord({ SheetId: 3, RecordId: 'Bz' })
    ```
- **返回数据示例**:
    ```json
    {"fields":{"日期":"2023/02/21"},"id":"Bz"}
    ```

### 3. 删除行记录
- **调用方式**：`Application.Record.DeleteRecords()`
- **请求参数**：

| 属性      | 数据类型 | 默认值 | 必填 | 说明                 |
| --------- | -------- | ------ | ---- | -------------------- |
| `SheetId`   | `Number` | -      | 是   | 表Id                 |
| `RecordIds` | `Array[]`| -      | 是   | 表中需要删除的记录id |

- **返回值**：返回`Object`，包含基本信息说明如下

| 属性    | 数据类型 | 说明                                           |
| ------- | -------- | ---------------------------------------------- |
| `id`    | `String` | 记录Id                                         |
| `deleted`| `Bool`   | 是否删除成功 <br> "true"表示删除成功，"false"表示删除失败 |

- **代码示例**:
    ```javascript
    const result = Application.Record.DeleteRecords({ 
        SheetId: 3, 
        RecordIds: ['J', 'P', 'Q'] 
    })
    ```
- **返回数据示例**:
    ```json
    [
        {"deleted":true,"id":"J"},
        {"deleted":true,"id":"P"},
        {"deleted":true,"id":"Q"}
    ]
    ```

### 4. 更新行记录
- **调用方式**：`Application.Record.UpdateRecords()`
- **请求参数**：

| 属性    | 数据类型 | 默认值 | 必填 | 说明                                                                                                  |
| ------- | -------- | ------ | ---- | ----------------------------------------------------------------------------------------------------- |
| `SheetId`| `Number` | -      | 是   | 表Id                                                                                                  |
| `Records`| `Array[]`| -      | 是   | - 需要更新的表记录信息 <br> - 字段(Field) 格式说明见  附件2 <br> **示例请见下方说明** |

**`Records` 参数示例**:
```javascript
Records: [{
            id: 'A',
            fields: {
                 邮箱: '<EMAIL>',
                 多选: ['1', '2'],
                 "记录关联": {
                    "recordIds": ["I", "K"] 
                 }
            }
        }]
```

- **返回值**：返回`Array[]`，表示表的已更新的所有记录，每条记录包含基本信息说明如下

| 属性   | 数据类型 | 说明                                                       |
| ------ | -------- | ---------------------------------------------------------- |
| `id`   | `String` | 记录Id                                                     |
| `fields`| `Object` | 每条记录的所有字段信息 <br> 字段(Field) 格式说明见  附件2 |

- **代码示例**:
    ```javascript
    const records = Application.Record.UpdateRecords({
        SheetId: 5,
        Records: [{
            id: 'A',
            fields: {
                 邮箱: '<EMAIL>',
                 多选: ['1', '2'],
                 "记录关联": {
                    "recordIds": ["I", "K"] 
                 }
            }
        }]
    })
    ```
- **返回数据示例**:
    ```json
    [
      {
        "fields": {
          "邮箱": "<EMAIL>",
          "多选": ["1", "2"],
          "记录关联": {
            "recordIds": ["I", "K"]
          }
        },
        "id": "A"
      }
    ]
    ```

### 5. 创建行记录
- **调用方式**：`Application.Record.CreateRecords()`
- **请求参数**：

| 属性    | 数据类型 | 默认值 | 必填 | 说明                                                                                                   |
| ------- | -------- | ------ | ---- | ------------------------------------------------------------------------------------------------------ |
| `SheetId`| `Number` | -      | 是   | 表Id                                                                                                   |
| `Records`| `Array[]`| -      | 是   | 需要创建的表记录信息 <br> Field 格式说明见  附件2 <br> **示例请见下方说明**                 |

**`Records` 参数示例**:
```javascript
Records: [{
            fields: {
                 邮箱: '<EMAIL>',
                 多选: ['1', '2'],
                 "记录关联": {
                    "recordIds": ["I", "K"] 
                 }
            }
        }]
```

- **返回值**：返回`Array[]`，表示表的已创建的所有记录，每条记录包含基本信息说明如下

| 属性   | 数据类型 | 说明                                                       |
| ------ | -------- | ---------------------------------------------------------- |
| `id`   | `String` | 记录Id                                                     |
| `fields`| `Object` | 每条记录的所有字段信息 <br> 字段(Field) 格式说明见  附件2 |

- **代码示例**:
    - **创建邮箱和多选**
        ```javascript
        const records = Application.Record.CreateRecords({
            SheetId: 5,
            Records: [{
                fields: {
                    邮箱: '<EMAIL>',
                    多选: ['1', '2']
                }
            }, {
                fields: {
                    邮箱: '<EMAIL>',
                    多选: ['1', '2']
                }
            }]
        });
        ```
    - **创建联系人**
        ```javascript
        const records = Application.Record.CreateRecords({
            SheetId: 3,
            Records: [
              { fields: { '联系人': [{ name: 'yourname', nickName: 'yourname', id: '88888888', avatar_url: 'https://avatar.qwps.cn/avatar/5b2t57-U' }] } }
            ]
        });
        ```
- **返回数据示例**:
    ```json
    [
      {
        "fields": {
          "邮箱": "<EMAIL>",
          "多选": ["1", "2"]
        },
        "id": "A"
      },
      {
        "fields": {
          "邮箱": "<EMAIL>",
          "多选": ["1", "2"]
        },
        "id": "A"
      }
    ]
    ```

**重要实践：数据写入前的类型转换**

在使用 `Application.Record.CreateRecords()` 向WPS多维表写入数据时，必须确保提供的每个值都严格符合WPS目标字段所期望的数据类型，否则容易导致 `E_INVALID_REQUEST` 等API错误。实践建议：

```javascript
// 示例：在为CreateRecords准备数据时进行类型转换
var fields_for_this_record = {};
Object.keys(current_row_data).forEach(function(key) {
  var original_value = current_row_data[key];
  var value_to_insert;

  if (original_value === null || typeof original_value === 'undefined') {
    value_to_insert = ""; // 将null/undefined转换为空字符串
  } else if (original_value instanceof Date) {
    // 根据目标字段需要选择适当格式
    // value_to_insert = original_value.toISOString(); // 日期转ISO字符串
    // 或使用WPS期望的格式如:
    var year = original_value.getFullYear();
    var month = String(original_value.getMonth() + 1).padStart(2, '0');
    var day = String(original_value.getDate()).padStart(2, '0');
    value_to_insert = year + "/" + month + "/" + day; // 转成 "YYYY/MM/DD" 格式
  } else {
    value_to_insert = String(original_value); // 其他所有类型转字符串 (适配MultiLineText)
  }
  fields_for_this_record[String(key)] = value_to_insert;
});
// 现在可以安全地创建记录
// var records_to_create = [{ fields: fields_for_this_record }];
// Application.Record.CreateRecords({ SheetId: sheetId, Records: records_to_create });
```

特别注意：
- 如果目标字段是 `MultiLineText`（或其他文本类型），所有值都应显式转换为字符串
- 处理 `null` 和 `undefined` 值时，应转换为空字符串或其他有意义的占位符
- 日期对象需要格式化为符合WPS期望的格式（如 `YYYY/MM/DD`）

### 6. 获取上传附件或图片的URL
- **调用方式**：`Application.Record.GetAttachmentURL()`
- **返回值**: 返回`String`，为获取上传附件或图片的URL，打开该URL可进行附件或图片下载
- **请求参数**:
    **注意**：必须至少传入1个参数`Attachment` 或者传入2个参数`UploadId`和 `Source`

| 属性       | 数据类型 | 默认值 | 必填 | 说明                                                              |
| ---------- | -------- | ------ | ---- | ----------------------------------------------------------------- |
| `Attachment`| `String` | -      |      | 附件                                                              |
| `UploadId` | `String` | -      |      | 上传文件id                                                        |
| `Source`   | `String` | -      |      | source参数必须为`"upload_ks3"`（本地上传）或`"cloud"`（云上传） |

- **代码示例**:
    ```javascript
    const resultURL1 = Application.Record.GetAttachmentURL({
        Attachment: "IKWRCBAAKA|upload_ks3|image/png|QQ图片20230214165215.png|12070||549*106"
    });

    // or

    const resultURL2 = Application.Record.GetAttachmentURL({
        UploadId: "IKWRCBAAKA",
        Source: "upload_ks3"
    });
    ```
- **返回数据示例**:
    ```
    https://ksc-bj-beta.ag.wps.cn/api/object/2_d1a59486c82747aa92305bfc8c4f95bb/compatible
    ```

## Selection 选区

### 1. 获取当前选中视图
- **调用方式**：`Application.Selection.GetActiveView()`
- **返回值**：返回`Object`，表示当前选中视图的信息，包含基本信息说明如下:

| 属性         | 数据类型 | 说明                                   |
| ------------ | -------- | -------------------------------------- |
| `sheetId`    | `Number` | 表Id (注意：返回示例为 Number，文档原文为 String，此处以示例为准) |
| `viewId`     | `String` | 视图Id                                 |
| `name`       | `String` | 视图名称                               |
| `recordsCount`| `Number` | 视图的记录数量 (注意：返回示例无此字段)   |
| `type`       | `String` | 视图类型 <br> 视图类型说明详见附件3 |

- **代码示例**:
    ```javascript
    const view = Application.Selection.GetActiveView()
    ```
- **返回数据示例**:
    ```json
    {
      "sheetId": 4,
      "viewId": "D",
      "name": "表",
      "type": "Grid"
    }
    ```

### 2. 获取选中记录信息
    a.  这里返回的 `records`是一个二维数组，因为选中记录可以是不连续的，如果是连续的记录，那么可以直接去 `records[0]`
- **调用方式**：`Application.Selection.GetSelectionRecords()`
- **返回值**：返回`Array[]`，表示多维表的选中的所有记录，每条记录包含基本信息说明如下:

| 属性   | 数据类型 | 说明                                                       |
| ------ | -------- | ---------------------------------------------------------- |
| `id`   | `String` | 记录Id                                                     |
| `fields`| `Object` | 每条记录的所有字段信息 <br> 具体字段类型说明详见  附件2 |

- **代码示例**:
    ```javascript
    const selectedRecords = Application.Selection.GetSelectionRecords()
    ```
- **返回数据示例**:
    ```json
    [
      [
        {
          "id": "BH6",
          "fields": [ // Note: Original example shows fields as an array of field definitions, not a key-value object.
            {"type": "MultiLineText", "name": "名称", "id": "H", "value": ""},
            {"type": "SingleSelect", "name": "分类", "id": "I", "value": ""}
          ]
        },
        {
          "id": "BH7",
          "fields": [
            {"type": "MultiLineText", "name": "名称", "id": "H", "value": ""},
            {"type": "SingleSelect", "name": "分类", "id": "I", "value": ""}
          ]
        }
      ]
    ]
    ```

### 3. 设置选中的视图
💡由于脚本执行机制改变，此 API 已废弃，请勿使用
- **调用方式**：`Application.Selection.SetActiveView()`
- **请求参数**:

| 属性    | 数据类型 | 默认值 | 必填 | 说明               |
| ------- | -------- | ------ | ---- | ------------------ |
| `SheetId`| `Number` | -      | 是   | 表Id               |
| `ViewId` | `String` | -      | 是   | 需要被选中的视图Id |

- **返回值**：返回 `Object`，内含选中视图的各种信息，视图信息中的字段说明如下所示:

| 属性         | 数据类型 | 说明                                   |
| ------------ | -------- | -------------------------------------- |
| `sheetId`    | `Number` | 表Id                                   |
| `viewId`     | `String` | 视图Id                                 |
| `name`       | `String` | 视图名称                               |
| `recordsCount`| `Number` | 视图的记录数量 (注意：返回示例无此字段) |
| `type`       | `String` | 视图类型 <br> 视图类型说明详见附件3 |

- **代码示例**:
    ```javascript
    // Application.Selection.SetActiveView({ SheetId: 27, ViewId: 'Q' }); // API 已废弃
    ```
- **返回数据示例 (仅供参考，API已废弃)**:
    ```json
    {"sheetId": 2, "viewId": "Q", "name": "表", "type": "Grid"}
    ```

### 4. 获取当前选中表
- **调用方式**：`Application.Selection.GetActiveSheet()`
- **返回值**：返回`Object`，内含选中表的基本信息，记录包含基本信息说明如下:

| 属性        | 数据类型 | 说明   |
| ----------- | -------- | ------ |
| `sheetId`   | `String` or `Number` | 表Id (返回示例为Number) |
| `name`      | `String` | 表名称 |
| `description`| `String` | 表的描述 |

- **代码示例**:
    ```javascript
    const sheet = Application.Selection.GetActiveSheet()
    ```
- **返回数据示例**:
    ```json
    {"sheetId": 4, "name": "商品表", "description": ""}
    ```

# 附件2：多维表字段类型说明
- 可以参考 [示例](https://kdocs.cn/l/ctx1jAV1xJhR) (假设此链接为相关示例)

---
## 多行文本 (MultiLineText)
- **Type**: `MultiLineText`
- **创建字段格式**: 无特殊要求
- **设置字段值传入形式**: 字符串 / 无特殊格式要求
- **读取字段值传出形式**: 字符串
- **备注**: -

---
## 日期 (Date)
- **Type**: `Date`
- **创建字段格式**: 无特殊要求
- **设置字段值传入形式**: 字符串 / `"yyyy/mm/dd"`
- **读取字段值传出形式**: 字符串 / `"yyyy/mm/dd"`
- **备注**: -

---
## 时间 (Time)
- **Type**: `Time`
- **创建字段格式**: 无特殊要求
- **设置字段值传入形式**: 字符串 / `"hh:mm:ss"`
- **读取字段值传出形式**: 字符串 / `"hh:mm:ss"`
- **备注**: -

---
## 数值 (Number)
- **Type**: `Number`
- **创建字段格式**: 无特殊要求
- **设置字段值传入形式**: 数值 / 无格式
- **读取字段值传出形式**: 数值
- **备注**: -

---
## 货币 (Currency)
- **Type**: `Currency`
- **创建字段格式**: 无特殊要求
- **设置字段值传入形式**: 数值 / 无格式
- **读取字段值传出形式**: 数值
- **备注**: -

---
## 百分比 (Percentage)
- **Type**: `Percentage`
- **创建字段格式**: 无特殊要求
- **设置字段值传入形式**: 数值 / 无格式
- **读取字段值传出形式**: 数值
- **备注**: -

---
## 身份证 (ID)
- **Type**: `ID`
- **创建字段格式**: 无特殊要求
- **设置字段值传入形式**: 字符串 / 符合身份证规则
- **读取字段值传出形式**: 字符串
- **备注**: -

---
## 电话 (Phone)
- **Type**: `Phone`
- **创建字段格式**: 无特殊要求
- **设置字段值传入形式**: 字符串 / 符合电话规则
- **读取字段值传出形式**: 字符串
- **备注**: -

---
## 电子邮箱 (Email)
- **Type**: `Email`
- **创建字段格式**: 无特殊要求
- **设置字段值传入形式**: 字符串 / 符合邮箱规则
- **读取字段值传出形式**: 字符串
- **备注**: -

---
## 超链接 (Url)
- **Type**: `Url`
- **创建字段格式**:
    ```json
    {
        "name": "超链接",
        "type": "Url",
        "displayText": "跳转" // 可以额外传入一个参数 displayText：指定超链接显示文本
    }
    ```
- **设置字段值传入形式**:
    1. 字符串 / 符合 Url 规则
    2. 结构体（适用于非按钮状态）：
       ```json
       {
         "address": "www.kdocs.cn",
         "displayText": "金山文档"
       }
       ```
- **读取字段值传出形式**: 字符串
- **备注**: -

---
## 复选框 (Checkbox)
- **Type**: `Checkbox`
- **创建字段格式**: 无特殊要求
- **设置字段值传入形式**: `true` / `false`
- **读取字段值传出形式**: `true` / `false`
- **备注**: -

---
## 单选项 (SingleSelect)
- **Type**: `SingleSelect`
- **创建字段格式**: 需要额外传入选项值，至少一个
    ```json
    {
        "name": "单选项",
        "type": "SingleSelect",
        "items": [{ "value": "item1" }]
    }
    ```
- **设置字段值传入形式**: 字符串 / 匹配选项内容
- **读取字段值传出形式**: 字符串
- **备注**: -

---
## 多选项 (MultipleSelect)
- **Type**: `MultipleSelect`
- **创建字段格式**: 需要额外传入选项值，至少一个
    ```json
    {
        "name": "多选项",
        "type": "MultipleSelect",
        "items": [{ "value": "item1" }, { "value": "item2" }]
    }
    ```
- **设置字段值传入形式**: 字符串数组 / 匹配选项内容
- **读取字段值传出形式**: 字符串数组
- **备注**: -

---
## 等级 (Rating)
- **Type**: `Rating`
- **创建字段格式**: 需要额外传入一个 最大等级, 最大等级大于0小于等于5
    ```json
    {
        "name": "等级",
        "type": "Rating",
        "max": 5,
    }
    ```
- **设置字段值传入形式**: 数值 /  大于 0 并且 小于 最大等级
- **读取字段值传出形式**: 数值
- **备注**: -

---
## 进度条 (Complete)
- **Type**: `Complete`
- **创建字段格式**: 无特殊要求
- **设置字段值传入形式**: 数值 /    大于等于0 并且 小于 1
- **读取字段值传出形式**: 数值（小数）
- **备注**: -

---
## 联系人 (Contact)
- **Type**: `Contact`
- **创建字段格式**: 需要额外传入两个参数: `"multipleContacts": <bool>` 是否支持多个联系人, `"noticeNewContact": <bool>` 是否通知联系人
    ```json
    {
        "name": "联系人",
        "type": "Contact",
        "multipleContacts": false,
        "noticeNewContact": false
    }
    ```
- **设置字段值传入形式**: 参数是数组，元素的格式是结构体。每个结构体必须包含 `id` 参数，可选 `nickname` 及 `avatar_url` 参数。
    - `id`, `nickname` 及 `avatar_url` 均为字符串
    - 同时存在 `nickname` 及 `avatar_url` 参数时，会用它们设置/更新 `id` 的联系人信息
    - **示例**：
        ```json
        [
          {"id": "{string_content}", "nickName": "{string_content}"},
          {"id": "{string_content_2}", "nickName": "{string_content_2}", "avatar_url": "{url_string}"}
        ]
        // 或者单个联系人
        // {"id": "{string_content}", "nickName": "{string_content}"}
        ```
- **读取字段值传出形式**: (与设置形式类似，通常为数组，元素包含id, nickName, avatar_url)
- **备注**: -

---
## 附件 (Attachment)
- **Type**: `Attachment`
- **创建字段格式**: 无特殊要求
- **设置字段值传入形式**:
    1. 参数是数组
    2. 每个附件信息，必须包含字段:
        - `uploadId`：上传的附件 ID
        - `fileName`：附件名称
        - `source`：类型 `'upload_ks3'`
        - `type`：`'image/jpeg'` 或者 `'image/png'`等
        - `size`：图片尺寸
        - `imgSize`：图片宽高
"照片/附件": [{
  "uploadId": object_id,
  "fileName": "照片.jpg",
  "source": 'upload_ks3',
  "type": "image/jpeg",
  "size": 4470,
  "imgSize": '180*180',
}]
- **读取字段值传出形式**: (通常为数组，每个元素包含fileName, downloadUrl, type, size, imgSize, uploadId等)
- **备注**: -

---
## 关联 (Link)
- **Type**: `Link`
- **创建字段格式**: 需要额外传入二个参数: `"linkSheet": <关联表 ID>`, `"multipleLinks": <bool>` 是否关联多条记录
    ```json
    {
        "name": "关联",
        "type": "Link",
        "linkSheet":  1,      // 关联表 ID
        "multipleLinks": false // 是否关联多条记录
    }
    ```
- **设置字段值传入形式**: 对应关联表的行记录ID数组。格式为：
    ```json
    {
        "recordIds": []
    }
    ```
- **读取字段值传出形式**: (通常为数组，每个元素包含id和关联记录的主字段值)
- **备注**: -

---
## 富文本 (Note)
- **Type**: `Note`
- **创建字段格式**: 无特殊要求
- **设置字段值传入形式**: 不支持设值
- **读取字段值传出形式**: 类似 `{"fileId":"PKS7GAQAGE","shortSummary":"rich 1","modifyDate":1658981246958}"
- **备注**: 自动同步富文本文档内容

---
## 编号 (AutoNumber)
- **Type**: `AutoNumber`
- **创建字段格式**: 无特殊要求
- **设置字段值传入形式**: 不支持设值
- **读取字段值传出形式**: 数值
- **备注**: 自动类型

---
## 创建者 (CreatedBy)
- **Type**: `CreatedBy`
- **创建字段格式**: 无特殊要求
- **设置字段值传入形式**: 不支持设值
- **读取字段值传出形式**: 类似 `{"id": "{string_content}", "nickName": "{string_content}"}`
- **备注**: 自动类型

---
## 创建时间 (CreatedTime)
- **Type**: `CreatedTime`
- **创建字段格式**: 无特殊要求
- **设置字段值传入形式**: 不支持设值
- **读取字段值传出形式**: 字符串 / "yyyy/mm/dd hh:mm:ss"
- **备注**: 自动类型

---
## 公式 (Formula)
- **Type**: `Formula`
- **创建字段格式**: 无特殊要求 (通常在UI中设置公式本身)
- **设置字段值传入形式**: 不支持设值
- **读取字段值传出形式**: 根据公式的值类型, 形式可能为文本/数值/逻辑值/联系人/时间/日期
- **备注**: 自动类型

---
## 引用 (Lookup)
- **Type**: `Lookup`
- **创建字段格式**: 无特殊要求 (通常在UI中设置引用关系和字段)
- **设置字段值传入形式**: 不支持设值
- **读取字段值传出形式**: 与被引用形式相同
- **备注**: 自动类型

# 附件3： 多维表视图类型说明
- 可以参考 [示例](https://kdocs.cn/l/ctx1jAV1xJhR) (假设此链接为相关示例)

| 视图类型 | 说明     |
| -------- | -------- |
| `Grid`   | 表视图   |
| `Kanban` | 看板视图 |
| `Gallery`| 画册视图 |
| `Form`   | 表单视图 |
| `Gantt`  | 甘特视图 |

# 附件4：筛选记录说明

## 1. Object筛选参数格式
```json
{
    "mode": "AND", // 选填。表示各筛选条件之间的逻辑关系。只能是"AND"或"OR"。缺省值为"AND"
    "criteria": [  // filter结构体内必填。包含筛选条件的数组。每个字段上只能有一个筛选条件
        {
            "field": "名称", // 必填。根据 preferId 与否，需要填入字段名或字段id
            "op": "Intersected", // 必填。表示具体的筛选规则，见下
            "values": [ // 必填。表示筛选规则中的值。数组形式。
                "多维表", // 值为字符串时表示文本匹配
                "12345"
            ]
        },
        {
            "field": "数量",
            "op": "greager",
            "values": [
                "1"
            ]
        }
    ]
}
```
注 1：这里的 `mode` 必须大写，否则会出错
注 2：这里的 `values`，必须是一个数组，传 `["多维表"]`，相当于传 `[{ type: 'Text', value: '多维表' }]`，即不传默认帮你补充 Text 类型
注 3：复选框的值，`values: ['0']` 代表否，`value: ['1']` 代表是 (应为 `values: ["1"]` 或 `values: [true]`)

## 2. 筛选条件 `op` 参数说明
- `"Equals"`: 等于
- `"NotEqu"`: 不等于
- `"Greater"`: 大于
- `"GreaterEqu"`: 大等于
- `"Less"`: 小于
- `"LessEqu"`: 小等于
- `"GreaterEquAndLessEqu"`: 介于（取等）
- `"LessOrGreater"`: 介于（不取等） (通常应为 `BetweenExclusive` 或类似)
- `"BeginWith"`: 开头是
- `"EndWith"`: 结尾是
- `"Contains"`: 包含
- `"NotContains"`: 不包含
- `"Intersected"`: 指定值
- `"Empty"`: 为空
- `"NotEmpty"`: 不为空

`values[]`数组内的元素为字符串时，表示文本匹配。
各筛选规则独立地限制了`values`数组内最多允许填写的元素数，当`values`内元素数超过阈值时，该筛选规则将失效。
a. "为空、不为空"不允许填写元素；
b. "介于"允许最多填写2个元素；
c. "指定值"允许填写65535个元素；
d. 其他规则允许最多填写1个元素

## 3. 日期的动态筛选
- 具体例子可查看 [https://kdocs.cn/l/ctx1jAV1xJhR?linkname=ynYAEqeO7r](https://kdocs.cn/l/ctx1jAV1xJhR?linkname=ynYAEqeO7r)

目前还支持对日期进行动态筛选，此时`values[]`内的元素需以结构体的形式给出：
```json
"filter": {
    "mode": "AND",
    "criteria": [
        {
            "field": "日期",
            "op": "Equals",
            "values": [
                {
                    "dynamicType": "lastMonth",
                    "type": "DynamicSimple"
                }
            ]
        }
    ]
}
```
上述示例对应的筛选条件为"等于上一个月"。
要使用日期动态筛选，`values[]`内的结构体需要指定`"type": "DynamicSimple"`，当`"op"`为`"Equals"`时，`"dynamicType"`可以为如下的值（大小写不敏感）：
- `"today"`: 今天
- `"yesterday"`: 昨天
- `"tomorrow"`: 明天
- `"last7Days"`: 最近7天
- `"last30Days"`: 最近30天
- `"thisWeek"`: 本周
- `"lastWeek"`: 上周
- `"nextWeek"`：下周
- `"thisMonth"`: 本月
- `"lastMonth"`: 上月
- `"nextMonth"`: 次月

当`"op"`为`"Greater"`或`"Less"`时，`"dynamicType"`只能是昨天、今天或明天。

---

## 重要开发实践与避坑指南 (根据实际调试经验总结)

在WPS AirScript的开发过程中，遵循一些最佳实践并注意常见的陷阱，可以显著提高开发效率和脚本的稳定性。以下是基于实际调试经验总结的关键点：

1.  **严格的API参数类型与值校验：**
    *   **`SheetId` 必须为数字**：所有接受 `SheetId` 的WPS API都要求其为数字类型。从 `Context.argv` 或其他API获取后，务必使用 `Number()` 进行转换和校验。例如，`Number(params.sheetId)`。
    *   **字段类型精确匹配**：使用 `Application.Sheet.CreateSheet` 或 `Application.Field.CreateFields` 创建表或字段时，提供的字段类型 (`type`) 必须是WPS API明确支持的类型。例如，没有 `DateTime` 类型，对于日期时间字符串，应使用 `MultiLineText` 或拆分为 `Date` 和 `Time` 字段。请参考官方最新的有效字段类型列表。
    *   **`Context.argv` 参数结构**：从自动化流程传递到 `Context.argv` 的参数，即使逻辑上是单个值（如记录ID、表ID），也可能以数组形式（如 `{"数据表ID": [87]}`）出现。脚本中接收时，应检查其类型 (`Array.isArray()`)，如果是数组且预期为单个值，则取其第一个元素 (e.g., `sheetIdFromContext = Array.isArray(sheetIdFromContext) && sheetIdFromContext.length > 0 ? sheetIdFromContext[0] : sheetIdFromContext;`)。

2.  **精确的名称匹配：**
    *   查找或比较表名、字段名时，WPS API通常执行严格的、区分大小写的字符串匹配。一个微小的拼写错误（如 `MyTable` vs `MyTable ` 或我们遇到的 `ScriptExecutionLogs_WorkshopTim` vs `ScriptExecutionLogs_WorkshopTime`）都会导致匹配失败，从而可能引发非预期的行为（如重复创建表）。
    *   在代码中定义表名或字段名常量时，务必与WPS多维表中的实际名称或期望名称完全一致。使用 `String(nameFromApi).trim() === String(expectedName).trim()` 进行比较可以增加一些稳健性，但根本上要确保定义和使用的一致性。

3.  **JavaScript特性与环境兼容性：**
    *   **`async/await`慎用**：尽管AirScript支持ES6，但对 `async/await` 的支持可能不完整或在特定上下文中（如顶层或非async函数内）存在问题，可能导致"不支持await"等运行时错误。大多数 `Application.*` API调用本身是同步的。如非必要（例如，调用 `HTTP.fetch` 且需要处理其Promise特性），建议优先采用同步编码风格，以避免兼容性问题。
    *   **循环遍历API返回数组**：WPS官方文档建议，为确保稳定可靠地遍历从 `Application.*` API（如 `Application.Sheet.GetSheets()`）返回的数组状结果，应优先使用传统的 `for` 循环 ( `for (let i = 0; i < array.length; i++)`)，而非 `forEach` 等现代迭代方法，因后者在AirScript环境中的行为可能与预期不一致。

4.  **对象构造的稳健性 (API参数)：**
    *   WPS官方文档提示，在使用如 `Application.Field.CreateFields` 或 `Application.Record.CreateRecords` 时，若需要构造包含多个对象（如字段定义、记录数据）的数组作为参数，直接使用 `.map(item => ({...}))` 的方式有时可能导致对象属性"丢失"或API调用失败（例如，对象内的 `name` 属性可能未被正确传递）。
    *   **推荐做法**：当遇到此类问题时，应改用传统的 `for` 循环，在循环中显式创建每个对象 (`let field_object = {}; field_object.name = ...;`)并设置其属性，然后将这些对象 `push` 到一个数组中，再将该数组传递给API。

5.  **全面的日志与错误处理：**
    *   **大量使用 `console.log`**：在开发和调试阶段，对关键变量的值、类型 (`typeof`)、API调用的输入参数和输出结果进行日志记录至关重要。`JSON.stringify()` 可以帮助查看对象和数组的完整结构。
    *   **结构化日志**：对于复杂脚本，可以像我们调试中实现的那样，建立一个日志缓冲区 (`GLOBAL_LOG_BUFFER`)，并将格式化的日志条目（包含时间戳、级别、消息）存入其中。
    *   **日志持久化**：可以将日志缓冲区的内容写入专门的WPS多维日志表，便于追踪和问题排查。注意，写入日志表的操作本身不应再触发主日志缓冲机制（即日志写入函数内部应使用 `console.log` 等直接输出），以避免递归。
    *   **`try...catch` 包裹API调用**：所有对WPS API的调用以及其他可能抛出异常的操作（如HTTP请求）都应包裹在 `try...catch` 块中，以便捕获和记录错误详情（如 `e.message` 和 `e.stack`）。

6.  **自动化流程参数命名一致性：**
    *   当脚本依赖于自动化流程通过 `Context.argv` 传入参数时（例如 `数据表ID`，`记录ID`），务必确保自动化流程配置中传递给脚本的参数键名与脚本中读取 `Context.argv` 时使用的键名完全一致（包括大小写和语言）。例如，如果脚本使用 `Context.argv["数据表ID"]`，则自动化流程中对应的参数名也必须是 "数据表ID"。

遵循这些实践，可以帮助您更顺利地开发和维护WPS AirScript脚本，减少调试时间，并提高脚本的健壮性。

---
