# 多维表结构信息获取脚本使用说明

## 脚本概述

本脚本用于获取WPS多维表格中所有表、视图、字段和字段格式等详细信息，帮助您快速了解多维表的完整结构。

## 主要功能

### 🎯 核心功能
- **获取所有表信息**：表名、表ID、记录数量、主字段等基本信息
- **获取视图信息**：视图类型、视图名称、记录数量等详细信息
- **获取字段信息**：字段类型、字段名称、特殊属性等完整信息
- **中文化说明**：将英文字段类型转换为易懂的中文说明
- **规范化日志记录**：按照WPS AirScript通用日志记录规范，自动将执行日志写入"脚本执行日志"表

### 📊 输出信息包含

#### 表基本信息
- 表ID和表名称
- 记录总数
- 主字段ID

#### 视图信息
- 视图ID和名称
- 视图类型（表视图、看板视图、画册视图等）
- 视图中的记录数量
- 视图类型的中文说明

#### 字段详细信息（完整配置）
- **基本信息**：字段ID、名称、类型、中文类型说明
- **是否为主字段**：标识主要字段
- **特殊属性**：
  - 单选/多选字段：完整的选项列表及选项数量
  - 等级字段：最大等级值
  - 联系人字段：多联系人支持、新联系人通知设置
  - 超链接字段：显示文本配置
  - 关联字段：关联表信息、多条关联设置
  - 货币字段：货币符号
  - 数字字段：精度设置
  - 字段描述：额外说明信息
- **创建配置**：用于复制表结构的完整JSON配置（**重要！用于创建新表**）

## 使用方法

### 在WPS多维表格中使用

1. **打开WPS多维表格**
2. **进入开发功能**
   - 在多维表格中找到"开发"功能入口
   - 点击进入脚本编辑界面

3. **复制脚本代码**
   - 将 `get_table_structure.js` 文件中的完整代码复制
   - 粘贴到WPS AirScript编辑器中

4. **运行脚本**
   - 点击"运行"按钮执行脚本
   - 查看控制台输出的详细信息
   - 脚本执行完成后，会自动创建"脚本执行日志"表并写入执行日志

### 输出格式

脚本会提供两种格式的输出：

#### 1. 结构化文本报告
```
============================================================
多维表结构信息汇总报告
============================================================
扫描时间: 2025-07-17 17:45:22
表的总数: 3
============================================================

【表 1】商品表 (ID: 3)
  记录数量: 304
  主字段ID: F

  视图列表 (3 个):
    1. 表视图 (表视图) - 304 条记录
       ID: C, 类型: Grid
       说明: 传统的表格形式展示数据
    2. 看板视图 (看板视图) - 304 条记录
       ID: I, 类型: Kanban
       说明: 看板形式展示，适合任务管理

  字段列表 (4 个):
    1. 名称 (多行文本) [主字段]
       ID: F, 类型: MultiLineText
       说明: 支持多行文本输入的字段
    2. 分类 (单选项)
       ID: G, 类型: SingleSelect
       说明: 单选下拉列表字段
       选项: A, B, C
```

#### 2. JSON格式数据
```json
{
  "summary": {
    "totalTables": 3,
    "scanTime": "2025-07-17 17:45:22",
    "description": "多维表结构信息汇总"
  },
  "tables": [
    {
      "basic": {
        "id": 3,
        "name": "商品表",
        "recordsCount": 304,
        "primaryFieldId": "F"
      },
      "views": [...],
      "fields": [...]
    }
  ]
}
```

## 字段类型说明

脚本支持以下所有WPS多维表字段类型的识别和说明：

| 英文类型 | 中文名称 | 说明 |
|---------|---------|------|
| MultiLineText | 多行文本 | 支持多行文本输入的字段 |
| Date | 日期 | 日期字段，格式为YYYY/MM/DD |
| Time | 时间 | 时间字段，格式为HH:mm:ss |
| Number | 数值 | 数字字段，支持小数 |
| Currency | 货币 | 货币金额字段 |
| Percentage | 百分比 | 百分比数值字段 |
| ID | 身份证 | 身份证号码字段 |
| Phone | 电话 | 电话号码字段 |
| Email | 电子邮箱 | 邮箱地址字段 |
| Url | 超链接 | 网址链接字段 |
| Checkbox | 复选框 | 布尔值字段，true/false |
| SingleSelect | 单选项 | 单选下拉列表字段 |
| MultipleSelect | 多选项 | 多选下拉列表字段 |
| Rating | 等级 | 评级字段，支持1-5星等级 |
| Complete | 进度条 | 进度百分比字段，0-1之间的小数 |
| Contact | 联系人 | 联系人信息字段 |
| Attachment | 附件 | 文件附件字段 |
| Link | 关联 | 关联其他表记录的字段 |
| Note | 富文本 | 富文本文档字段，自动同步 |
| AutoNumber | 编号 | 自动递增编号字段 |
| CreatedBy | 创建者 | 记录创建者信息，自动字段 |
| CreatedTime | 创建时间 | 记录创建时间，自动字段 |
| Formula | 公式 | 公式计算字段，自动计算 |
| Lookup | 引用 | 引用其他字段的值，自动字段 |

## 视图类型说明

| 英文类型 | 中文名称 | 说明 |
|---------|---------|------|
| Grid | 表视图 | 传统的表格形式展示数据 |
| Kanban | 看板视图 | 看板形式展示，适合任务管理 |
| Gallery | 画册视图 | 画册形式展示，适合图片内容 |
| Form | 表单视图 | 表单形式，适合数据录入 |
| Gantt | 甘特视图 | 甘特图形式，适合项目进度管理 |

## 特殊功能

### 时区处理
- 脚本自动处理时区问题，确保所有时间记录都使用中国时区（UTC+8）
- 避免了AirScript运行环境可能存在的时区偏差问题

### 规范化日志记录
- 严格按照WPS AirScript通用日志记录规范实现
- 自动创建"脚本执行日志"表（如果不存在）
- 支持INFO、ERROR、WARN、DEBUG四个日志级别
- 每次执行后将完整的日志信息写入多维表，便于跟踪和问题排查
- 日志表包含两个字段：
  - **执行时间**：脚本开始执行的时间
  - **日志内容**：完整的执行日志，包含时间戳和级别标识

### 兼容性优化
- 使用传统的`for`循环而非`forEach`，确保在AirScript环境中的稳定性
- 对API返回数据进行严格的类型检查和转换
- 提供详细的错误处理和日志记录

### 数据完整性
- 识别并提取字段的特殊属性（如选项列表、最大等级等）
- 标识主字段
- 提供字段和视图的详细说明

## 注意事项

### 运行环境
- 确保在WPS多维表格的AirScript环境中运行
- 需要有访问多维表格数据的权限

### 性能考虑
- 对于包含大量表和字段的多维表格，脚本执行可能需要一些时间
- 脚本会在控制台输出详细的处理进度

### 错误处理
- 脚本包含完善的错误处理机制
- 如果遇到权限问题或网络问题，会在控制台显示详细的错误信息

## 日志记录详情

### 日志表结构
脚本执行后会自动创建"脚本执行日志"表，包含以下字段：
- **执行时间** (多行文本)：记录脚本开始执行的准确时间
- **日志内容** (多行文本)：包含完整的执行日志，格式如下：
  ```
  2025-07-17 17:45:22.123 [INFO] 多维表结构信息获取脚本开始执行
  2025-07-17 17:45:22.125 [INFO] 脚本功能: 获取当前多维表中所有表、字段和字段格式等信息
  2025-07-17 17:45:22.127 [INFO] 开始获取多维表结构信息...
  2025-07-17 17:45:22.134 [INFO] 成功获取到 3 个表
  2025-07-17 17:45:22.135 [DEBUG] 正在处理表: 商品表 (ID: 3)
  2025-07-17 17:45:22.137 [DEBUG] 表 商品表 包含 3 个视图
  2025-07-17 17:45:22.139 [DEBUG] 表 商品表 包含 4 个字段
  2025-07-17 17:45:22.141 [DEBUG] 完成表 商品表 的信息收集
  2025-07-17 17:45:22.143 [INFO] 多维表结构信息获取完成！
  2025-07-17 17:45:22.145 [INFO] 脚本执行成功完成
  ```

### 日志级别说明
- **INFO**：重要的业务信息和执行状态
- **DEBUG**：详细的执行过程信息
- **WARN**：警告信息，如跳过无效数据
- **ERROR**：错误信息和异常详情

### 日志查看方式
1. **控制台实时查看**：脚本执行时在控制台实时显示日志
2. **表格持久化存储**：执行完成后在"脚本执行日志"表中查看完整日志
3. **便于问题排查**：每次执行都会创建新的日志记录，便于历史追溯

## 输出用途

获取的结构信息可以用于：

1. **数据库设计分析**：了解现有表结构，为优化提供依据
2. **数据迁移准备**：为数据迁移项目提供准确的结构信息
3. **文档编写**：为项目文档提供准确的表结构说明
4. **系统集成**：为与其他系统集成提供接口设计参考
5. **权限管理**：了解字段类型和用途，合理设置访问权限

## 技术特点

- **严格遵循WPS AirScript最佳实践**
- **规范化日志记录系统**：按照官方通用日志记录规范实现
- **完整的中文注释和文档**
- **健壮的错误处理机制**
- **兼容性优化设计**
- **结构化的数据输出**
- **时区处理优化**
- **自动日志表管理**：自动创建和维护日志表结构

---

## 配套工具：品牌表复制脚本

为了方便基于获取的表结构信息创建新品牌的表，我们还提供了配套的表复制脚本：

### 📋 `create_brand_tables.js` - 品牌表结构复制脚本

#### 功能说明
- 基于现有品牌表结构为新品牌创建相同的表
- 自动替换表名和字段中的品牌名称
- 保持完整的字段配置和视图结构
- 适用于多品牌管理场景

#### 使用场景
您的需求：为不同品牌（如画朴、卓芝）创建必备的表结构
- 画朴品牌有：「画朴」目标管理、「画朴」完成度、「画朴」个人绩效对比
- 卓芝品牌需要：「卓芝」目标管理、「卓芝」完成度、「卓芝」个人绩效对比

#### 配置方法
在脚本中修改 `BRAND_TABLE_CONFIG` 配置：
```javascript
const BRAND_TABLE_CONFIG = {
  sourceBrand: "画朴",      // 源品牌名称
  targetBrand: "卓芝",      // 目标品牌名称
  tableTypes: [             // 需要复制的表类型
    "目标管理",
    "完成度", 
    "个人绩效对比"
  ]
};
```

#### 使用步骤
1. 先运行 `get_table_structure.js` 确认表结构信息
2. 根据需要修改 `create_brand_tables.js` 中的配置
3. 运行复制脚本创建新品牌的表

#### 安全特性
- 自动检查表是否已存在，避免重复创建
- 完整的日志记录，便于跟踪创建过程
- 详细的结果报告，显示成功/失败/跳过的表

---

## 故障排除

如果您在使用过程中遇到任何问题，请检查：
1. 是否在正确的WPS多维表格环境中运行
2. 是否有足够的权限访问多维表格数据
3. 网络连接是否正常
4. 控制台中的具体错误信息
5. 查看"脚本执行日志"表中的详细执行记录
6. 确认日志记录功能是否正常工作（如果日志写入失败，控制台会显示相应错误） 